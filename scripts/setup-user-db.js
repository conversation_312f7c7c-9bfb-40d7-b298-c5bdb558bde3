#!/usr/bin/env node
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const USER_DB_PATH = path.join(__dirname, '../db/users.db');
const USER_SCHEMA_PATH = path.join(__dirname, '../prisma/schema.users.prisma');

// Ensure the db directory exists
const dbDir = path.join(__dirname, '../db');
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Set environment variable for the user database
process.env.USER_DATABASE_URL = `file:${USER_DB_PATH}`;

console.log('Setting up user database...');

try {
  // Generate the Prisma client for the user database
  console.log('Generating Prisma client for user database...');
  execSync(`npx prisma generate --schema=${USER_SCHEMA_PATH}`, { stdio: 'inherit' });
  
  // Create or update the user database
  console.log('Creating/updating user database schema...');
  execSync(`npx prisma db push --schema=${USER_SCHEMA_PATH} --accept-data-loss`, { stdio: 'inherit' });
  
  // Seed with initial data if needed
  if (process.argv.includes('--seed')) {
    console.log('Seeding user database with initial data...');
    // You can add seeding logic here or call a separate script
    // execSync(`node seed-user-db.js`, { stdio: 'inherit' });
  }
  
  console.log('User database setup completed successfully!');
} catch (error) {
  console.error('Error setting up user database:', error.message);
  process.exit(1);
}