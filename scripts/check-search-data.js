const { PrismaClient } = require('../lib/generated/user-prisma');

const userPrisma = new PrismaClient({
  datasources: {
    db: {
      url: 'file:./db/users.db',
    },
  },
});

async function checkSearchData() {
  try {
    console.log('Checking search tracking data...\n');

    // Check search history
    const searchHistory = await userPrisma.searchHistory.findMany({
      orderBy: {
        searchedAt: 'desc',
      },
      take: 10,
    });

    console.log('Recent Search History:');
    console.log('=====================');
    if (searchHistory.length === 0) {
      console.log('No search history found.');
    } else {
      searchHistory.forEach((search, index) => {
        console.log(`${index + 1}. Query: "${search.searchQuery}"`);
        console.log(`   Location: ${search.searchLocation || 'N/A'}`);
        console.log(`   User ID: ${search.userId || 'Anonymous'}`);
        console.log(`   Searched At: ${search.searchedAt}`);
        console.log('');
      });
    }

    // Check search term stats
    const searchStats = await userPrisma.searchTermStats.findMany({
      orderBy: {
        searchCount: 'desc',
      },
      take: 10,
    });

    console.log('\nPopular Search Terms:');
    console.log('====================');
    if (searchStats.length === 0) {
      console.log('No search term statistics found.');
    } else {
      searchStats.forEach((stat, index) => {
        console.log(`${index + 1}. Term: "${stat.searchTerm}"`);
        console.log(`   Search Count: ${stat.searchCount}`);
        console.log(`   Last Searched: ${stat.lastSearchedAt}`);
        console.log('');
      });
    }

    // Summary
    console.log('\nSummary:');
    console.log('========');
    console.log(`Total searches recorded: ${searchHistory.length}`);
    console.log(`Unique search terms: ${searchStats.length}`);

  } catch (error) {
    console.error('Error checking search data:', error);
  } finally {
    await userPrisma.$disconnect();
  }
}

checkSearchData();
