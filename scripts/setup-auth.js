#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Define paths
const USER_SCHEMA_PATH = path.join(__dirname, '../prisma/schema.users.prisma');
const USER_DB_DIR = path.join(__dirname, '../db');
const USER_DB_PATH = path.join(USER_DB_DIR, 'users.db');

// Ensure the db directory exists
if (!fs.existsSync(USER_DB_DIR)) {
  console.log('Creating db directory...');
  fs.mkdirSync(USER_DB_DIR, { recursive: true });
}

// Check if the user database exists
if (!fs.existsSync(USER_DB_PATH)) {
  console.log('User database not found, creating it...');
  
  try {
    // Generate the Prisma client for the user database
    console.log('Generating Prisma client for user database...');
    execSync(`npx prisma generate --schema=${USER_SCHEMA_PATH}`, { stdio: 'inherit' });
    
    // Create or update the user database
    console.log('Creating/updating user database schema...');
    execSync(`npx prisma db push --schema=${USER_SCHEMA_PATH} --accept-data-loss`, { stdio: 'inherit' });
    
    console.log('User database setup completed successfully!');
  } catch (error) {
    console.error('Error setting up user database:', error.message);
    process.exit(1);
  }
} else {
  console.log('User database already exists.');
  
  try {
    // Generate the Prisma client for the user database
    console.log('Generating Prisma client for user database...');
    execSync(`npx prisma generate --schema=${USER_SCHEMA_PATH}`, { stdio: 'inherit' });
    
    // Update the user database schema if needed
    console.log('Updating user database schema...');
    execSync(`npx prisma db push --schema=${USER_SCHEMA_PATH}`, { stdio: 'inherit' });
    
    console.log('User database updated successfully!');
  } catch (error) {
    console.error('Error updating user database:', error.message);
    process.exit(1);
  }
}

// Make the script executable
try {
  fs.chmodSync(__filename, '755');
} catch (error) {
  console.warn('Could not make script executable:', error.message);
}
