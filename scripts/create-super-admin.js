const { PrismaClient } = require('@prisma/client')

// Initialize Prisma client for users database
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL_USERS || 'file:./db/users.db'
    }
  }
})

async function createSuperAdmin() {
  const email = process.argv[2]
  
  if (!email) {
    console.error('Usage: node scripts/create-super-admin.js <email>')
    console.error('Example: node scripts/create-super-admin.js <EMAIL>')
    process.exit(1)
  }
  
  try {
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email }
    })
    
    if (!user) {
      console.error(`User with email ${email} not found.`)
      console.error('Please make sure the user has registered first.')
      process.exit(1)
    }
    
    // Update user role to SUPER_ADMIN
    const updatedUser = await prisma.user.update({
      where: { email },
      data: { role: 'SUPER_ADMIN' }
    })
    
    console.log(`✅ Successfully promoted ${email} to SUPER_ADMIN`)
    console.log(`User ID: ${updatedUser.id}`)
    console.log(`Name: ${updatedUser.name || 'Not set'}`)
    console.log(`Role: ${updatedUser.role}`)
    
  } catch (error) {
    console.error('Error promoting user to super admin:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

createSuperAdmin()
