const { PrismaClient } = require('../lib/generated/user-prisma')
const bcrypt = require('bcryptjs')

// Initialize Prisma client for users database
const prisma = new PrismaClient()

async function seedSuperAdmin() {
  const email = ''
  const password = ''
  const name = ''
  
  try {
    // Check if super admin already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })
    
    if (existingUser) {
      console.log(`Super admin with email ${email} already exists.`)
      
      // Update to ensure they have SUPER_ADMIN role and correct password
      const hashedPassword = await bcrypt.hash(password, 12)
      
      const updatedUser = await prisma.user.update({
        where: { email },
        data: {
          role: 'SUPER_ADMIN',
          passwordHash: hashedPassword,
          name: name,
          isEmailVerified: true
        }
      })
      
      console.log(`✅ Updated existing user to SUPER_ADMIN`)
      console.log(`User ID: ${updatedUser.id}`)
      console.log(`Email: ${updatedUser.email}`)
      console.log(`Name: ${updatedUser.name}`)
      console.log(`Role: ${updatedUser.role}`)
      return
    }
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12)
    
    // Create the super admin user
    const superAdmin = await prisma.user.create({
      data: {
        email,
        name,
        passwordHash: hashedPassword,
        role: 'SUPER_ADMIN',
        isEmailVerified: true
      }
    })
    
    console.log(`✅ Successfully created super admin user`)
    console.log(`User ID: ${superAdmin.id}`)
    console.log(`Email: ${superAdmin.email}`)
    console.log(`Name: ${superAdmin.name}`)
    console.log(`Role: ${superAdmin.role}`)
    console.log(`Password: ${password}`)
    console.log(`\n🔐 Login credentials:`)
    console.log(`Email: ${email}`)
    console.log(`Password: ${password}`)
    console.log(`\n🌐 Access admin dashboard at: http://localhost:3001/admin/dashboard`)
    
  } catch (error) {
    console.error('Error creating super admin:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

seedSuperAdmin()
