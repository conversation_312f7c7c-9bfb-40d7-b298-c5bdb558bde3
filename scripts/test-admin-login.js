const { PrismaClient } = require('../lib/generated/user-prisma')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function testAdminLogin() {
  const email = '<EMAIL>'
  const password = 'Iloveolivia0!'
  
  try {
    console.log('🔍 Looking for user with email:', email)
    
    const user = await prisma.user.findUnique({
      where: { email }
    })
    
    if (!user) {
      console.log('❌ User not found in database')
      return
    }
    
    console.log('✅ User found:')
    console.log('  ID:', user.id)
    console.log('  Email:', user.email)
    console.log('  Name:', user.name)
    console.log('  Role:', user.role)
    console.log('  Has Password Hash:', !!user.passwordHash)
    console.log('  Email Verified:', user.isEmailVerified)
    
    if (!user.passwordHash) {
      console.log('❌ User has no password hash')
      return
    }
    
    console.log('\n🔐 Testing password verification...')
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash)
    
    if (isPasswordValid) {
      console.log('✅ Password verification successful!')
      console.log('✅ Login should work with these credentials')
    } else {
      console.log('❌ Password verification failed')
      console.log('❌ The stored password hash does not match the provided password')
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

testAdminLogin()
