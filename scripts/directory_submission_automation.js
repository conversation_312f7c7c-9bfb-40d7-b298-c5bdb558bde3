const fs = require('fs');
const path = require('path');
const puppeteer = require('puppeteer');

const SITE_URL = 'https://emergencyfind.com';
const SITE_TITLE = 'Emergency Services Directory | Find 24/7 Help Near You';
const SITE_DESCRIPTION = 'Connect with 24/7 emergency service providers instantly when you need them most. Find emergency services in your area with real-time availability. Verified and reliable providers.';

async function readDirectorySites() {
  const filePath = path.resolve(__dirname, '../directory_sites_list.txt');
  const content = fs.readFileSync(filePath, 'utf-8');
  const lines = content.split('\\n');
  const urls = lines.map(line => {
    const match = line.match(/\\d+\\.\\s*(\\S+)/);
    if (match) {
      let url = match[1];
      if (!url.startsWith('http')) {
        url = 'http://' + url;
      }
      return url;
    }
    return null;
  }).filter(Boolean);
  return urls;
}

async function submitToDirectory(page, url) {
  try {
    await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 30000 });
    console.log('Visited:', url);

    // Attempt to find submission page or form link
    const submissionLinkSelectors = [
      'a[href*="submit"]',
      'a[href*="add"]',
      'a[href*="directory"]',
      'a[href*="listing"]',
      'a[href*="suggest"]',
      'a[href*="contribute"]',
      'a[href*="register"]',
      'a[href*="signup"]',
      'a[href*="create"]',
    ];

    let submissionPageUrl = null;
    for (const selector of submissionLinkSelectors) {
      const linkHandle = await page.$(selector);
      if (linkHandle) {
        submissionPageUrl = await page.evaluate(el => el.href, linkHandle);
        break;
      }
    }

    if (submissionPageUrl) {
      await page.goto(submissionPageUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
      console.log('Navigated to submission page:', submissionPageUrl);
    } else {
      console.log('No submission page link found on:', url);
      return false;
    }

    // Try to fill form fields for URL, title, description
    // This is a heuristic approach due to variability of forms
    const urlSelectors = ['input[name*="url"]', 'input[id*="url"]', 'input[placeholder*="URL"]', 'input[type="url"]'];
    const titleSelectors = ['input[name*="title"]', 'input[id*="title"]', 'input[placeholder*="title"]'];
    const descSelectors = ['textarea[name*="description"]', 'textarea[id*="description"]', 'textarea[placeholder*="description"]'];

    for (const sel of urlSelectors) {
      const el = await page.$(sel);
      if (el) {
        await el.click({ clickCount: 3 });
        await el.type(SITE_URL);
        break;
      }
    }

    for (const sel of titleSelectors) {
      const el = await page.$(sel);
      if (el) {
        await el.click({ clickCount: 3 });
        await el.type(SITE_TITLE);
        break;
      }
    }

    for (const sel of descSelectors) {
      const el = await page.$(sel);
      if (el) {
        await el.click({ clickCount: 3 });
        await el.type(SITE_DESCRIPTION);
        break;
      }
    }

    // Attempt to submit the form
    const submitSelectors = ['input[type="submit"]', 'button[type="submit"]', 'button:contains("Submit")', 'button:contains("Add")'];
    for (const sel of submitSelectors) {
      const btn = await page.$(sel);
      if (btn) {
        await btn.click();
        console.log('Form submitted on:', page.url());
        return true;
      }
    }

    console.log('No submit button found on:', page.url());
    return false;

  } catch (error) {
    console.error('Error submitting to', url, error.message);
    return false;
  }
}

(async () => {
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();

  const directoryUrls = await readDirectorySites();

  for (const url of directoryUrls) {
    console.log('Processing:', url);
    const success = await submitToDirectory(page, url);
    if (!success) {
      console.log('Submission failed or skipped for:', url);
    }
    // Wait a bit between submissions to avoid rate limits
    await page.waitForTimeout(5000);
  }

  await browser.close();
})();
