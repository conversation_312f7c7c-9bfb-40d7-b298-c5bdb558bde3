const { getRecentSearches } = require('../lib/search-tracking-service');

async function testRecentSearches() {
  try {
    console.log('Testing getRecentSearches...\n');

    // Test with no userId (should return global recent searches)
    const recentSearches = await getRecentSearches(undefined);
    
    console.log('Recent searches returned:');
    console.log('========================');
    
    if (recentSearches.length === 0) {
      console.log('No recent searches found.');
    } else {
      recentSearches.forEach((search, index) => {
        console.log(`${index + 1}. Query: "${search.query}"`);
        console.log(`   Location: ${search.location || 'N/A'}`);
        console.log(`   Timestamp: ${search.timestamp}`);
        console.log('');
      });
    }

    console.log(`Total searches: ${recentSearches.length}`);

  } catch (error) {
    console.error('Error testing recent searches:', error);
  }
}

testRecentSearches();
