#!/usr/bin/env node
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const MAIN_SCHEMA_PATH = path.join(__dirname, '../prisma/schema.prisma');
const USER_SCHEMA_PATH = path.join(__dirname, '../prisma/schema.users.prisma');
const MAIN_DB_PATH = path.join(__dirname, '../db/urgent_services.db');
const USER_DB_PATH = path.join(__dirname, '../db/users.db');

// Ensure the db directory exists
const dbDir = path.join(__dirname, '../db');
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Check if we need to generate both schemas or just one
const generateAll = process.argv.includes('--all');
const generateMain = generateAll || process.argv.includes('--main');
const generateUser = generateAll || process.argv.includes('--user');

// If no specific flag is provided, generate both
if (!generateMain && !generateUser) {
  console.log('No specific schema specified, generating both schemas...');
  generateMain = true;
  generateUser = true;
}

// Generate the main schema client
if (generateMain) {
  console.log('Generating main Prisma client...');
  try {
    // Set environment variable for the main database
    process.env.DATABASE_URL = `file:${MAIN_DB_PATH}`;
    
    // Generate the Prisma client for the main database
    execSync(`npx prisma generate --schema=${MAIN_SCHEMA_PATH}`, { stdio: 'inherit' });
    
    console.log('Main Prisma client generated successfully!');
  } catch (error) {
    console.error('Error generating main Prisma client:', error.message);
    if (!generateUser) {
      process.exit(1);
    }
  }
}

// Generate the user schema client
if (generateUser) {
  console.log('Generating user Prisma client...');
  try {
    // Set environment variable for the user database
    process.env.USER_DATABASE_URL = `file:${USER_DB_PATH}`;
    
    // Check if user schema file exists
    if (!fs.existsSync(USER_SCHEMA_PATH)) {
      console.error(`User schema file not found at ${USER_SCHEMA_PATH}`);
      console.log('Please create the user schema file first.');
      process.exit(1);
    }
    
    // Generate the Prisma client for the user database
    execSync(`npx prisma generate --schema=${USER_SCHEMA_PATH}`, { stdio: 'inherit' });
    
    // Create or update the user database if it doesn't exist
    if (!fs.existsSync(USER_DB_PATH)) {
      console.log('User database not found, creating it...');
      execSync(`npx prisma db push --schema=${USER_SCHEMA_PATH} --accept-data-loss`, { stdio: 'inherit' });
    }
    
    console.log('User Prisma client generated successfully!');
  } catch (error) {
    console.error('Error generating user Prisma client:', error.message);
    process.exit(1);
  }
}

console.log('Schema generation completed!');