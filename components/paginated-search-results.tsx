"use client"

import React, { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { SearchResults } from "./search-results"
import { SearchLoading } from "./search-loading"

export function PaginatedSearchResults({
  initialBusinesses,
  initialTotalCount,
  initialPage,
  pageSize,
  query,
  location,
  category,
  distance,
  open24,
  verified,
  rating,
  sortBy,
}: {
  initialBusinesses: any[]
  initialTotalCount: number
  initialPage: number
  pageSize: number
  query: string
  location: string
  category: string
  distance?: number
  open24?: boolean
  verified?: boolean
  rating?: number
  sortBy: "rating" | "distance"
}) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [page, setPage] = useState(initialPage)
  const [businesses, setBusinesses] = useState(initialBusinesses)
  const [totalCount, setTotalCount] = useState(initialTotalCount)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    // When page changes, fetch new data
    async function fetchPageData() {
      setLoading(true)
      const params = new URLSearchParams(searchParams.toString())
      params.set("page", page.toString())

      // Set required parameters
      if (query) params.set("q", query)
      if (location) params.set("location", location)
      if (category) params.set("category", category)

      // Set filter parameters
      if (distance) params.set("distance", distance.toString())
      if (open24) params.set("open24", "true")
      if (verified) params.set("verified", "true")
      if (rating) params.set("rating", rating.toString())

      // Set sortBy parameter
      if (sortBy) params.set("sortBy", sortBy)

      const res = await fetch(`/api/search?${params.toString()}`)
      if (res.ok) {
        const data = await res.json()
        setBusinesses(data.businesses)
        setTotalCount(data.totalCount)
      }
      setLoading(false)
    }

    if (page !== initialPage) {
      fetchPageData()
    }
  }, [page, query, location, category, distance, open24, verified, rating, sortBy, initialPage, searchParams])

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
    // Update URL without full reload
    const params = new URLSearchParams(searchParams.toString())
    params.set("page", newPage.toString())
    router.replace(`/search?${params.toString()}`)
  }

  if (loading) {
    return <SearchLoading />
  }

  return (
    <SearchResults
      businesses={businesses}
      totalCount={totalCount}
      currentPage={page}
      pageSize={pageSize}
      query={query}
      location={location}
      category={category}
      onPageChange={handlePageChange}
    />
  )
}
