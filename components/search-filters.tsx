"use client"

import { useState, useEffect } from "react"

let cachedCategories: string[] | null = null
import { useRouter, useSearchParams } from "next/navigation"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Star, Search } from "lucide-react"
import { getCategoriesAction } from "@/app/actions"

export function SearchFilters({
  category = "",
  initialDistance = 10,
  initialOpen24Hours = false,
  initialVerified = false,
  initialRating = null
}: {
  category?: string,
  initialDistance?: number,
  initialOpen24Hours?: boolean,
  initialVerified?: boolean,
  initialRating?: number | null
}) {
  const router = useRouter()
  const searchParams = useSearchParams()

  // Store both the current UI state and the last applied state
  const [distance, setDistance] = useState([initialDistance])
  const [open24Hours, setOpen24Hours] = useState(initialOpen24Hours)
  const [verified, setVerified] = useState(initialVerified)
  const [rating, setRating] = useState<number | null>(initialRating)

  // Track if filters have been modified since last apply
  const [filtersModified, setFiltersModified] = useState(false)

  // We're removing the automatic URL update on filter change
  // This will prevent the page from refreshing when the slider changes
  // Instead, we'll only update the URL when the Apply Filters button is clicked
  const [categories, setCategories] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [categoryFilter, setCategoryFilter] = useState(category)
  const [filteredCategories, setFilteredCategories] = useState<string[]>([])

  // Fetch categories asynchronously with caching
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;

    const fetchCategories = async () => {
      if (cachedCategories) {
        setCategories(cachedCategories)
        setFilteredCategories(cachedCategories)
        setIsLoading(false)
        console.log("Loaded categories from cache")
        return
      }

      try {
        setIsLoading(true)
        console.log("Fetching categories from server...")

        // Set timeout fallback to avoid indefinite loading spinner
        timeoutId = setTimeout(() => {
          console.warn("Categories fetch timeout, setting isLoading to false")
          setIsLoading(false)
        }, 10000)

        const allCategories = await getCategoriesAction()
        console.log(`Received ${allCategories.length} categories from server`)

        if (allCategories.length > 0) {
          console.log("Sample categories:", allCategories.slice(0, 5))
        } else {
          console.warn("No categories returned from server")
        }

        cachedCategories = allCategories
        setCategories(allCategories)
        setFilteredCategories(allCategories)
      } catch (error) {
        console.error("Error fetching categories:", error)
      } finally {
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
        setIsLoading(false)
      }
    }

    fetchCategories()

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [])

  // Initialize state from URL parameters or props
  useEffect(() => {
    // First check URL parameters
    const distanceParam = searchParams.get('distance')
    if (distanceParam) {
      setDistance([parseInt(distanceParam)])
    } else if (initialDistance) {
      // Fall back to initialDistance prop if no URL parameter
      setDistance([initialDistance])
    }

    const open24Param = searchParams.get('open24')
    if (open24Param) {
      setOpen24Hours(open24Param === 'true')
    } else {
      // Fall back to initialOpen24Hours prop
      setOpen24Hours(initialOpen24Hours)
    }

    const verifiedParam = searchParams.get('verified')
    if (verifiedParam) {
      setVerified(verifiedParam === 'true')
    } else {
      // Fall back to initialVerified prop
      setVerified(initialVerified)
    }

    const ratingParam = searchParams.get('rating')
    if (ratingParam) {
      setRating(parseInt(ratingParam))
    } else if (initialRating) {
      // Fall back to initialRating prop
      setRating(initialRating)
    }
  }, [searchParams, initialDistance, initialOpen24Hours, initialVerified, initialRating])

  const handleCategoryChange = (selectedCategory: string | null) => {
    // Create a new URLSearchParams object
    const params = new URLSearchParams()

    // Preserve existing query and location parameters
    const query = searchParams.get('q')
    const location = searchParams.get('location')

    // Add the preserved parameters
    if (query) params.set('q', query)
    if (location) params.set('location', location)

    // Add current filter values
    if (distance[0]) params.set('distance', distance[0].toString())
    if (open24Hours) params.set('open24', 'true')
    if (verified) params.set('verified', 'true')
    if (rating) params.set('rating', rating.toString())

    // Update or remove the category parameter
    if (selectedCategory) {
      console.log(`Setting category filter to: "${selectedCategory}"`)
      params.set('category', selectedCategory)
    } else {
      console.log('Clearing category filter')
      // Don't add category parameter
    }

    // Keep the current page at 1 when changing filters
    params.set('page', '1')

    // Log the new URL for debugging
    const newUrl = `/search?${params.toString()}`
    console.log(`Navigating to: ${newUrl}`)

    // Use router.push instead of window.location.href to avoid a full page refresh
    router.push(newUrl)
  }

  const applyFilters = () => {
    console.log('Applying filters:')
    console.log(`- Distance: ${distance[0]} miles`)
    console.log(`- Open 24 Hours: ${open24Hours}`)
    console.log(`- Verified: ${verified}`)
    console.log(`- Rating: ${rating || 'Any'}`)

    // Create a new URLSearchParams object based on the current URL
    const params = new URLSearchParams()

    // Preserve existing query and location parameters
    const query = searchParams.get('q')
    const location = searchParams.get('location')
    const categoryParam = searchParams.get('category')

    // Add the preserved parameters
    if (query) params.set('q', query)
    if (location) params.set('location', location)
    if (categoryParam) params.set('category', categoryParam)

    // Update parameters with filter values
    // Always include distance parameter, even if it's 0
    params.set('distance', distance[0].toString())
    console.log(`Setting distance parameter to ${distance[0]}`)

    if (open24Hours) {
      params.set('open24', 'true')
      console.log('Setting open24 parameter to true')
    } else {
      // Explicitly remove the parameter if it's false
      params.delete('open24')
    }

    if (verified) {
      params.set('verified', 'true')
      console.log('Setting verified parameter to true')
    } else {
      // Explicitly remove the parameter if it's false
      params.delete('verified')
    }

    if (rating) {
      params.set('rating', rating.toString())
      console.log(`Setting rating parameter to ${rating}`)
    } else {
      // Explicitly remove the parameter if it's null
      params.delete('rating')
    }

    // Keep the current page at 1 when changing filters
    params.set('page', '1')

    // Log the new URL for debugging
    const newUrl = `/search?${params.toString()}`
    console.log(`Navigating to: ${newUrl}`)

    // Use router.push instead of window.location.href to avoid a full page refresh
    router.push(newUrl)
  }

  const resetFilters = () => {
    console.log('Resetting all filters')

    // Create a new URLSearchParams object
    const params = new URLSearchParams()

    // Keep only the search query and location
    const query = searchParams.get('q')
    const location = searchParams.get('location')

    if (query) params.set('q', query)
    if (location) params.set('location', location)

    // Reset to page 1
    params.set('page', '1')

    // Reset local state
    setDistance([10]) // Reset to default
    setOpen24Hours(false)
    setVerified(false)
    setRating(null)

    // Log the new URL for debugging
    const newUrl = `/search?${params.toString()}`
    console.log(`Navigating to: ${newUrl}`)

    // Use router.push instead of window.location.href to avoid a full page refresh
    router.push(newUrl)
  }

  // Sync categoryFilter state with category prop on mount and when category changes
  useEffect(() => {
    setCategoryFilter(category)
  }, [category])

  // Filter categories based on search input
  useEffect(() => {
    if (categoryFilter.trim() === "") {
      setFilteredCategories(categories);
    } else {
      const filtered = categories.filter(cat =>
        cat.toLowerCase().includes(categoryFilter.toLowerCase())
      );
      setFilteredCategories(filtered);
    }
  }, [categories, categoryFilter]);

  // We'll show filtered categories in a scrollable container
  const displayedCategories = filteredCategories

  return (
    <div className="bg-white rounded-lg border p-4">
      <h2 className="font-bold text-lg mb-4">Filters</h2>

      <Accordion type="multiple" defaultValue={["category", "distance", "availability", "rating"]}>
        <AccordionItem value="category">
          <AccordionTrigger>
            Category
            {category && (
              <span className="ml-2 text-xs font-normal text-blue-600 truncate max-w-[100px]" title={category}>
                ({category})
              </span>
            )}
          </AccordionTrigger>
          <AccordionContent className="!overflow-visible" style={{ isolation: 'isolate' }}>
            <div className="space-y-2" style={{ position: 'relative', isolation: 'isolate' }}>
              {isLoading ? (
                <div className="py-4 text-center">
                  <div className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
                  <p className="mt-2 text-sm text-gray-500">Loading categories...</p>
                </div>
              ) : (
                <>
                  <div className="space-y-2 overflow-visible" style={{ position: 'relative' }}>
                    <div className={`flex items-center space-x-2 mb-3 py-1 px-1 rounded ${!category ? 'bg-blue-50' : ''}`}>
                      <input
                        type="radio"
                        id="all-categories"
                        name="category"
                        className="h-4 w-4 text-blue-600"
                        checked={!category}
                        onChange={() => handleCategoryChange(null)}
                      />
                      <Label
                        htmlFor="all-categories"
                        className={`cursor-pointer ${!category ? 'font-medium text-blue-700' : ''}`}
                      >
                        All Categories
                      </Label>
                    </div>

                    {/* Search input for categories */}
                    <div className="relative mb-3" style={{ zIndex: 50 }}>
                      <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 z-50" />
                      <Input
                        type="text"
                        placeholder={`Search ${categories.length} categories...`}
                        className="pl-8 py-1 h-8 text-sm w-full relative z-50 focus:outline-none  "
                        value={categoryFilter}
                        onChange={(e) => setCategoryFilter(e.target.value)}
                        style={{
                          minWidth: "100%",
                          position: "relative",
                          zIndex: 50
                        }}
                        // Add focus styles to ensure the input is visible when focused


                      />
                      {categoryFilter && (
                        <button
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 z-50"
                          onClick={() => setCategoryFilter("")}
                          style={{ zIndex: 60 }}
                        >
                          ×
                        </button>
                      )}
                    </div>

                    {/* Scrollable container for categories */}
                    <div
                      className="overflow-y-auto overflow-x-visible bg-white rounded-md max-h-[300px]"
                      style={{
                        scrollbarWidth: 'thin',
                        scrollbarColor: '#d1d5db transparent',
                        boxShadow: 'inset 0 0 4px rgba(0,0,0,0.05)',
                        position: 'relative',
                        zIndex: 10
                      }}
                    >
                      {displayedCategories.length > 0 ? (
                        displayedCategories.map((cat) => (
                          <div
                            key={cat}
                            className={`flex items-start space-x-2 py-1.5 px-1 hover:bg-gray-50 rounded transition-colors ${category === cat ? 'bg-blue-50' : ''}`}
                          >
                            <input
                              type="radio"
                              id={cat.toLowerCase().replace(/\s+/g, "-")}
                              name="category"
                              className="h-4 w-4 text-blue-600 flex-shrink-0 mt-0.5"
                              checked={category === cat}
                              onChange={() => handleCategoryChange(cat)}
                            />
                            <Label
                              htmlFor={cat.toLowerCase().replace(/\s+/g, "-")}
                              className={`cursor-pointer text-sm leading-tight ${category === cat ? 'font-medium text-blue-700' : ''}`}
                            >
                              {cat}
                            </Label>
                          </div>
                        ))
                      ) : (
                        <div className="py-2 text-center text-gray-500 text-sm">
                          {categoryFilter ? "No matching categories" : "No categories available"}
                        </div>
                      )}
                    </div>
                  </div>

                  {filteredCategories.length === 0 && categoryFilter && (
                    <p className="text-sm text-gray-500 text-center mt-2">
                      No categories match "{categoryFilter}"
                    </p>
                  )}
                </>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="distance">
          <AccordionTrigger>
            Distance
            {distance[0] !== 10 && (
              <span className="ml-2 text-xs font-normal text-blue-600">
                ({distance[0]} {distance[0] === 1 ? 'mile' : 'miles'})
              </span>
            )}
          </AccordionTrigger>
          <AccordionContent className="!overflow-visible">
            <div className="space-y-4">
              <Slider
                value={distance}
                max={50}
                step={1}
                onValueChange={(val) => {
                  setDistance(val);
                  setFiltersModified(true);
                }}
              />
              <div className="flex justify-between text-sm text-gray-500">
                <span>0 miles</span>
                <span className="font-medium text-blue-600">
                  {distance[0]} {distance[0] === 1 ? 'mile' : 'miles'}
                  {distance[0] === 0 && ' (exact location)'}
                </span>
                <span>50 miles</span>
              </div>
              {distance[0] === 0 && (
                <p className="text-xs text-gray-500 mt-1">
                  Setting distance to 0 miles will only show results at your exact location.
                </p>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="availability">
          <AccordionTrigger>
            Availability
            {(open24Hours || verified) && (
              <span className="ml-2 text-xs font-normal text-blue-600">
                {open24Hours && verified
                  ? "(24h, Verified)"
                  : open24Hours
                    ? "(24h)"
                    : "(Verified)"}
              </span>
            )}
          </AccordionTrigger>
          <AccordionContent className="!overflow-visible">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="open-24-hours"
                  checked={open24Hours}
                  onCheckedChange={(checked) => {
                    setOpen24Hours(!!checked);
                    setFiltersModified(true);
                  }}
                />
                <Label htmlFor="open-24-hours" className="cursor-pointer">Open 24 Hours</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="verified"
                  checked={verified}
                  onCheckedChange={(checked) => {
                    setVerified(!!checked);
                    setFiltersModified(true);
                  }}
                />
                <Label htmlFor="verified" className="cursor-pointer">Verified Providers</Label>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="rating">
          <AccordionTrigger>
            Rating
            {rating && (
              <span className="ml-2 text-xs font-normal text-blue-600">
                ({rating}+ stars)
              </span>
            )}
          </AccordionTrigger>
          <AccordionContent className="!overflow-visible">
            <div className="space-y-2">
              {[5, 4, 3, 2, 1].map((value) => (
                <div
                  key={value}
                  className={`flex items-center space-x-2 p-2 rounded-md cursor-pointer ${
                    rating === value ? "bg-blue-50" : ""
                  }`}
                  onClick={() => {
                    setRating(value === rating ? null : value);
                    setFiltersModified(true);
                  }}
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      setRating(value === rating ? null : value);
                      setFiltersModified(true);
                    }
                  }}
                >
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${i < value ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}`}
                      />
                    ))}
                  </div>
                  <span className="text-sm">{value}+ stars</span>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <div className="mt-6 space-y-2">
        <Button
          className={`w-full ${filtersModified ? 'bg-blue-700 hover:bg-blue-800' : ''}`}
          onClick={() => {
            applyFilters();
            setFiltersModified(false);
          }}
        >
          {filtersModified ? 'Apply Changes' : 'Apply Filters'}
        </Button>
        <Button
          variant="outline"
          className="w-full"
          onClick={() => {
            resetFilters();
            setFiltersModified(false);
          }}
        >
          Reset
        </Button>
      </div>
    </div>
  )
}
