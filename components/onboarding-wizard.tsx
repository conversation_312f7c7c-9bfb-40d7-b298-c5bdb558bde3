"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { CheckCircle2, Circle, ArrowLeft, ArrowRight } from "lucide-react"
import { cn } from "@/lib/utils"

export interface OnboardingStep {
  id: string
  title: string
  description: string
  component: React.ComponentType<OnboardingStepProps>
  isOptional?: boolean
}

export interface OnboardingStepProps {
  data: any
  updateData: (data: any) => void
  onNext: () => void
  onBack: () => void
  onSkip?: () => void
  isSubmitting?: boolean
}

interface OnboardingWizardProps {
  steps: OnboardingStep[]
  currentStepId: string
  onStepChange: (stepId: string) => void
  onComplete: () => void
  formData: any
  updateFormData: (data: any) => void
  isSubmitting?: boolean
  title?: string
  description?: string
}

export function OnboardingWizard({
  steps,
  currentStepId,
  onStepChange,
  onComplete,
  formData,
  updateFormData,
  isSubmitting = false,
  title = "Business Onboarding",
  description = "Let's get your business set up on our platform",
}: OnboardingWizardProps) {
  const currentStepIndex = steps.findIndex(step => step.id === currentStepId)
  const currentStep = steps[currentStepIndex]
  const progress = ((currentStepIndex + 1) / steps.length) * 100

  const handleNext = () => {
    if (currentStepIndex < steps.length - 1) {
      onStepChange(steps[currentStepIndex + 1].id)
    } else {
      onComplete()
    }
  }

  const handleBack = () => {
    if (currentStepIndex > 0) {
      onStepChange(steps[currentStepIndex - 1].id)
    }
  }

  const handleSkip = () => {
    handleNext()
  }

  if (!currentStep) {
    return <div>Step not found</div>
  }

  const StepComponent = currentStep.component

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto max-w-4xl px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{title}</h1>
          <p className="text-gray-600">{description}</p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <span className="text-sm font-medium text-gray-700">
              Step {currentStepIndex + 1} of {steps.length}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(progress)}% Complete
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Step Navigation */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div
                    className={cn(
                      "w-10 h-10 rounded-full flex items-center justify-center border-2 transition-colors",
                      index < currentStepIndex
                        ? "bg-green-500 border-green-500 text-white"
                        : index === currentStepIndex
                        ? "bg-blue-500 border-blue-500 text-white"
                        : "bg-white border-gray-300 text-gray-400"
                    )}
                  >
                    {index < currentStepIndex ? (
                      <CheckCircle2 className="w-5 h-5" />
                    ) : (
                      <span className="text-sm font-medium">{index + 1}</span>
                    )}
                  </div>
                  <div className="mt-2 text-center">
                    <div
                      className={cn(
                        "text-xs font-medium",
                        index <= currentStepIndex ? "text-gray-900" : "text-gray-400"
                      )}
                    >
                      {step.title}
                    </div>
                    {step.isOptional && (
                      <div className="text-xs text-gray-400">(Optional)</div>
                    )}
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={cn(
                      "w-16 h-0.5 mx-4 transition-colors",
                      index < currentStepIndex ? "bg-green-500" : "bg-gray-300"
                    )}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Current Step Content */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {currentStep.title}
              {currentStep.isOptional && (
                <span className="text-sm font-normal text-gray-500">(Optional)</span>
              )}
            </CardTitle>
            <CardDescription>{currentStep.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <StepComponent
              data={formData}
              updateData={updateFormData}
              onNext={handleNext}
              onBack={handleBack}
              onSkip={currentStep.isOptional ? handleSkip : undefined}
              isSubmitting={isSubmitting}
            />
          </CardContent>
        </Card>

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handleBack}
            disabled={currentStepIndex === 0 || isSubmitting}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>

          <div className="flex gap-2">
            {currentStep.isOptional && (
              <Button
                variant="ghost"
                onClick={handleSkip}
                disabled={isSubmitting}
              >
                Skip
              </Button>
            )}
            <Button
              onClick={handleNext}
              disabled={isSubmitting}
              className="flex items-center gap-2"
            >
              {currentStepIndex === steps.length - 1 ? (
                isSubmitting ? "Submitting..." : "Complete Setup"
              ) : (
                <>
                  Next
                  <ArrowRight className="w-4 h-4" />
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
