"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Upload, X, Image as ImageIcon, Loader2 } from "lucide-react"
import Image from "next/image"

interface BusinessImageUploadProps {
  currentImageUrl?: string
  onImageChange: (imageUrl: string | null) => void
  label?: string
  description?: string
  required?: boolean
}

export function BusinessImageUpload({
  currentImageUrl,
  onImageChange,
  label = "Business Image",
  description = "Upload a photo of your business, logo, or storefront. This helps customers recognize and trust your business.",
  required = false
}: BusinessImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadError, setUploadError] = useState<string | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Reset error state
    setUploadError(null)
    setIsUploading(true)

    try {
      // Create preview URL
      const objectUrl = URL.createObjectURL(file)
      setPreviewUrl(objectUrl)

      // Upload file
      const formData = new FormData()
      formData.append('image', file)

      const response = await fetch('/api/upload-business-image', {
        method: 'POST',
        body: formData,
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Upload failed')
      }

      // Clean up object URL
      URL.revokeObjectURL(objectUrl)
      
      // Update with server URL
      setPreviewUrl(result.imageUrl)
      onImageChange(result.imageUrl)

    } catch (error) {
      console.error('Upload error:', error)
      setUploadError(error instanceof Error ? error.message : 'Upload failed')
      setPreviewUrl(currentImageUrl || null)
    } finally {
      setIsUploading(false)
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleRemoveImage = async () => {
    if (previewUrl && previewUrl.startsWith('/uploads/')) {
      try {
        // Extract filename from URL
        const filename = previewUrl.split('/').pop()
        if (filename) {
          await fetch(`/api/upload-business-image?filename=${filename}`, {
            method: 'DELETE',
          })
        }
      } catch (error) {
        console.error('Error deleting image:', error)
      }
    }

    setPreviewUrl(null)
    onImageChange(null)
    setUploadError(null)
  }

  const triggerFileSelect = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="space-y-4">
      <div>
        <Label className="text-base font-medium">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
        <p className="text-sm text-gray-500 mt-1">
          {description}
        </p>
      </div>

      {uploadError && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-sm text-red-600">{uploadError}</p>
        </div>
      )}

      <Card className="border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors">
        <CardContent className="p-6">
          {previewUrl ? (
            <div className="space-y-4">
              <div className="relative aspect-video w-full max-w-md mx-auto rounded-lg overflow-hidden bg-gray-100">
                <Image
                  src={previewUrl}
                  alt="Business image preview"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
                {isUploading && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <Loader2 className="h-8 w-8 text-white animate-spin" />
                  </div>
                )}
              </div>
              
              <div className="flex gap-2 justify-center">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={triggerFileSelect}
                  disabled={isUploading}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Replace Image
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleRemoveImage}
                  disabled={isUploading}
                >
                  <X className="h-4 w-4 mr-2" />
                  Remove
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-900">
                  Upload a business image
                </p>
                <p className="text-xs text-gray-500">
                  JPEG, PNG, WebP, or GIF up to 5MB
                </p>
              </div>
              <Button
                type="button"
                variant="outline"
                className="mt-4"
                onClick={triggerFileSelect}
                disabled={isUploading}
              >
                {isUploading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Choose File
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <Input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp,image/gif"
        onChange={handleFileSelect}
        className="hidden"
        disabled={isUploading}
      />

      <div className="text-xs text-gray-500">
        <p>• Recommended size: 800x600 pixels or larger</p>
        <p>• Supported formats: JPEG, PNG, WebP, GIF</p>
        <p>• Maximum file size: 5MB</p>
      </div>
    </div>
  )
}
