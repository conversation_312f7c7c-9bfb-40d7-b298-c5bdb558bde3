"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { SearchIcon, MapPin } from "lucide-react"
import { LocationButton } from "@/components/location-button"
import { useLogger } from '@logtail/next'

export function Search({
  defaultQuery = "",
  defaultLocation = "",
}: {
  defaultQuery?: string
  defaultLocation?: string
}) {
  const router = useRouter()
  const log = useLogger()
  const [query, setQuery] = useState(defaultQuery)
  const [location, setLocation] = useState(defaultLocation)
  const [isSearching, setIsSearching] = useState(false)
  const [queryError, setQueryError] = useState<string | null>(null)
  
  // Log component mounting for debugging
  useEffect(() => {
    console.log("Search component mounted with defaults:", { defaultQuery, defaultLocation });
    log.info("Search component mounted", { defaultQuery, defaultLocation });
  }, [defaultQuery, defaultLocation, log]);

  const handleSearch = async (e: React.FormEvent) => {
    console.log("handleSearch triggered")
    e.preventDefault()

    if (!query.trim()) {
      setQueryError("Please enter a service you need")
      return
    }

    setQueryError(null)
    try {
      setIsSearching(true)

      const params = new URLSearchParams()
      params.append("q", query) // Always include query parameter
      if (location) params.append("location", location)

      console.log("Navigating to search with params:", params.toString())
      log.info("Initiating search", { query, location, params: params.toString() });

      // Use router.push with { scroll: false } to prevent scroll issues
      router.push(`/search?${params.toString()}`, { scroll: false })
    } catch (error) {
      console.error("Error during search:", error)
      log.error("Search error", { error: error instanceof Error ? error.message : String(error), query, location });
    } finally {
      setIsSearching(false)
    }
  }

  // Function to update the location field
  const handleLocationFound = (locationString: string) => {
    setLocation(locationString)
  }

  return (
    <form onSubmit={handleSearch} className="w-full max-w-4xl mx-auto">
      <div className="flex flex-col md:flex-row gap-2">
        <div className="relative flex-1">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <div className="relative">
          <Input
            type="text"
            placeholder="Search for a service (e.g., plumber, locksmith)"
            className={`pl-5 h-12 bg-white text-black ${queryError ? "border-red-500" : ""}`}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            aria-invalid={!!queryError}
            aria-describedby={queryError ? "query-error-message" : undefined}
          />
          {queryError && (
            <p id="query-error-message" className="text-red-500 text-sm mt-1 absolute">
              {queryError}
            </p>
          )}
          </div>
        </div>
        <div className="relative flex-1">
          <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <Input
            type="text"
            placeholder="Location (city, zip code)"
            className="pl-10 h-12 bg-white text-black pr-10"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
          />
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <LocationButton onLocationFound={handleLocationFound} />
          </div>
        </div>
        <Button
          type="submit"
          className="h-12 px-6 rounded-md border border-white hover:border-white transition disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={isSearching}
        >
          {isSearching ? (
            <>
              <span className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em] mr-2"></span>
              Searching...
            </>
          ) : (
            "Find Services"
          )}
        </Button>
      </div>
    </form>
  )
}
