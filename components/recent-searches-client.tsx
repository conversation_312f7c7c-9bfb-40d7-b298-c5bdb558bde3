"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Clock, X } from "lucide-react"
import { clearRecentSearchesAction, removeRecentSearchAction } from "@/app/actions"

type RecentSearch = {
  id: string
  query: string
  location: string
  timestamp: Date
}

interface RecentSearchesClientProps {
  initialSearches: RecentSearch[]
}

export function RecentSearchesClient({ initialSearches }: RecentSearchesClientProps) {
  const [searches, setSearches] = useState<RecentSearch[]>(initialSearches)

  const removeSearch = async (id: string) => {
    try {
      // TODO: Get userId from session when auth is implemented
      await removeRecentSearchAction('', id) // Empty userId for now
      setSearches(searches.filter((search) => search.id !== id))
    } catch (error) {
      console.error('Error removing search:', error)
      // Still remove from UI even if database update fails
      setSearches(searches.filter((search) => search.id !== id))
    }
  }

  const clearAllSearches = async () => {
    try {
      // TODO: Get userId from session when auth is implemented
      await clearRecentSearchesAction('') // Empty userId for now
      setSearches([])
    } catch (error) {
      console.error('Error clearing searches:', error)
      // Still clear UI even if database update fails
      setSearches([])
    }
  }

  const formatTime = (timestamp: Date) => {
    const now = new Date()
    const diff = now.getTime() - timestamp.getTime()

    if (diff < 1000 * 60 * 60) {
      return `${Math.floor(diff / (1000 * 60))} minutes ago`
    } else if (diff < 1000 * 60 * 60 * 24) {
      return `${Math.floor(diff / (1000 * 60 * 60))} hours ago`
    } else {
      return `${Math.floor(diff / (1000 * 60 * 60 * 24))} days ago`
    }
  }

  return (
    <div className="bg-white rounded-lg border divide-y">
      {searches.map((search) => (
        <div key={search.id} className="p-4 flex justify-between items-center">
          <div>
            <Link
              href={`/search?q=${encodeURIComponent(search.query)}&location=${encodeURIComponent(search.location)}`}
              className="font-medium hover:text-blue-600"
            >
              {search.query}
            </Link>
            <div className="text-sm text-gray-500 flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>{formatTime(search.timestamp)}</span>
              <span>•</span>
              <span>{search.location}</span>
            </div>
          </div>
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => removeSearch(search.id)}>
            <X className="h-4 w-4" />
            <span className="sr-only">Remove</span>
          </Button>
        </div>
      ))}
      {/* <div className="p-3 text-center">
        <Button variant="ghost" size="sm" onClick={clearAllSearches}>
          Clear All
        </Button>
      </div> */}
    </div>
  )
}
