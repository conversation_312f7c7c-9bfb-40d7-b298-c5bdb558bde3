import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON> } from "lucide-react"

// Generate mock reviews based on rating and count
function generateMockReviews(rating: number, count: number) {
  if (count === 0) return []

  // Create a distribution of ratings that averages to the given rating
  const reviews = []
  const names = [
    "John D<PERSON>",
    "Sarah M<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "Amanda S.",
    "Thomas B.",
    "<PERSON>",
  ]

  const comments = [
    "Excellent emergency service! They arrived quickly and solved my problem efficiently.",
    "Great service overall. They came out in the middle of the night to help.",
    "Fantastic service! Professional, courteous, and efficient.",
    "Very responsive and professional. Would use again.",
    "They arrived within 30 minutes of my call. Highly recommend!",
    "Good service but a bit pricey for what was done.",
    "Saved me in an emergency situation. Very grateful for their quick response.",
    "Professional staff and quality service. Will definitely call again if needed.",
    "They were available when no one else was. Great emergency service.",
    "Quick response time and thorough work. Exactly what you need in an emergency.",
  ]

  // Generate a realistic distribution of ratings
  const ratingDistribution = []
  if (rating >= 4.5) {
    // Mostly 5s with some 4s
    ratingDistribution.push(...Array(Math.floor(count * 0.7)).fill(5))
    ratingDistribution.push(...Array(Math.floor(count * 0.25)).fill(4))
    ratingDistribution.push(...Array(Math.floor(count * 0.05)).fill(3))
  } else if (rating >= 4.0) {
    // Mix of 5s and 4s with few 3s
    ratingDistribution.push(...Array(Math.floor(count * 0.5)).fill(5))
    ratingDistribution.push(...Array(Math.floor(count * 0.4)).fill(4))
    ratingDistribution.push(...Array(Math.floor(count * 0.1)).fill(3))
  } else if (rating >= 3.0) {
    // More varied distribution
    ratingDistribution.push(...Array(Math.floor(count * 0.3)).fill(5))
    ratingDistribution.push(...Array(Math.floor(count * 0.3)).fill(4))
    ratingDistribution.push(...Array(Math.floor(count * 0.3)).fill(3))
    ratingDistribution.push(...Array(Math.floor(count * 0.1)).fill(2))
  } else {
    // Lower ratings
    ratingDistribution.push(...Array(Math.floor(count * 0.1)).fill(5))
    ratingDistribution.push(...Array(Math.floor(count * 0.2)).fill(4))
    ratingDistribution.push(...Array(Math.floor(count * 0.3)).fill(3))
    ratingDistribution.push(...Array(Math.floor(count * 0.3)).fill(2))
    ratingDistribution.push(...Array(Math.floor(count * 0.1)).fill(1))
  }

  // Shuffle the distribution
  ratingDistribution.sort(() => Math.random() - 0.5)

  // Generate reviews (limit to 10 for display)
  const reviewCount = Math.min(count, 10)
  for (let i = 0; i < reviewCount; i++) {
    reviews.push({
      id: `r${i + 1}`,
      user: {
        name: names[i % names.length],
        avatar: "/placeholder.svg?height=40&width=40",
      },
      rating: ratingDistribution[i] || Math.round(rating),
      date: `${Math.floor(Math.random() * 6) + 1} ${Math.random() > 0.5 ? "weeks" : "months"} ago`,
      content: comments[i % comments.length],
    })
  }

  return reviews
}

export function BusinessReviews({
  businessId,
  rating,
  reviewCount,
}: {
  businessId: string
  rating: number
  reviewCount: number
}) {
  // Generate mock reviews based on the business rating and review count
  const reviews = generateMockReviews(rating, reviewCount)

  if (reviews.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="flex justify-between items-center mb-6">
          <h3 className="font-bold text-lg">Customer Reviews</h3>
          <Button>Write a Review</Button>
        </div>
        <div className="text-center py-6">
          <p className="text-gray-500">No reviews yet. Be the first to review!</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <div className="flex justify-between items-center mb-6">
        <h3 className="font-bold text-lg">Customer Reviews</h3>
        <Button>Write a Review</Button>
      </div>

      <div className="space-y-6">
        {reviews.map((review) => (
          <div key={review.id} className="border-b pb-6 last:border-0">
            <div className="flex items-start">
              <Avatar className="h-10 w-10">
                <AvatarImage src={review.user.avatar || "/placeholder.svg"} alt={review.user.name} />
                <AvatarFallback>{review.user.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div className="ml-4">
                <div className="flex items-center">
                  <h4 className="font-semibold">{review.user.name}</h4>
                  <span className="mx-2 text-gray-300">•</span>
                  <span className="text-sm text-gray-500">{review.date}</span>
                </div>
                <div className="flex my-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${i < review.rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}`}
                    />
                  ))}
                </div>
                <p className="text-gray-700 mt-2">{review.content}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {reviewCount > 10 && (
        <div className="mt-6 text-center">
          <Button variant="outline">Load More Reviews</Button>
        </div>
      )}
    </div>
  )
}
