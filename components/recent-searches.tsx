import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Clock, X } from "lucide-react"
import { getRecentSearchesAction } from "@/app/actions"
import { RecentSearchesClient } from "@/components/recent-searches-client"

type RecentSearch = {
  id: string
  query: string
  location: string
  timestamp: Date
}

export async function RecentSearches() {
  let searches: RecentSearch[] = []
  let loading = false

  try {
    // TODO: Get userId from session when auth is implemented
    searches = await getRecentSearchesAction(undefined)
  } catch (error) {
    console.error('Error fetching recent searches:', error)
    // Fall back to empty array
    searches = []
  }

  if (searches.length === 0) {
    return (
      <div className="bg-gray-100 rounded-lg p-6 text-center">
        <p className="text-gray-500">No recent searches</p>
      </div>
    )
  }

  // Pass the data to a client component for interactivity
  return <RecentSearchesClient initialSearches={searches} />
}
