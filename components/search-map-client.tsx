"use client"

import { useEffect, useRef } from "react"
import { type Business } from "@/lib/data"

interface SearchMapClientProps {
  businesses: Business[]
  query?: string
  location?: string
  category?: string
}

export function SearchMapClient({ businesses, query, location, category }: SearchMapClientProps) {
  const mapRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Mock map initialization
    if (mapRef.current) {
      mapRef.current.innerHTML = `
        <div class="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div class="bg-white p-4 rounded-md shadow-md text-center">
            <p class="font-medium mb-2">Interactive Map</p>
            <p class="text-gray-500 text-sm">Would display ${businesses.length} service providers</p>
            <p class="text-gray-500 text-sm mt-2">
              ${query ? `Search: "${query}"` : ""}
              ${location ? `Location: "${location}"` : ""}
              ${category ? `Category: "${category}"` : ""}
            </p>
          </div>
        </div>
      `
    }
  }, [businesses, query, location, category])

  return (
    <div ref={mapRef} className="relative h-[500px] w-full rounded-md overflow-hidden">
      {/* Map will be initialized here */}
    </div>
  )
}
