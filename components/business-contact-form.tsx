"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { PhoneInput } from "@/components/phone-input"

type ContactData = {
  phone: string
  email: string
  address: string
  city: string
  state: string
  zipCode: string
  serviceRadius: string
}

interface BusinessContactFormProps {
  data: ContactData
  updateData: (data: Partial<ContactData>) => void
  onNext: () => void
  onBack: () => void
}

export function BusinessContactForm({ data, updateData, onNext, onBack }: BusinessContactFormProps) {
  const [errors, setErrors] = useState<Partial<Record<keyof ContactData, string>>>({})

  const validate = () => {
    const newErrors: Partial<Record<keyof ContactData, string>> = {}

    if (!data.phone.trim()) {
      newErrors.phone = "Phone number is required"
    } else if (!/^\d{10}$/.test(data.phone.trim())) {
      newErrors.phone = "Please enter a valid 10-digit phone number"
    }

    if (!data.email.trim()) {
      newErrors.email = "Email is required"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email.trim())) {
      newErrors.email = "Please enter a valid email address"
    }

    if (!data.address.trim()) {
      newErrors.address = "Address is required"
    }

    if (!data.city.trim()) {
      newErrors.city = "City is required"
    }

    if (!data.state.trim()) {
      newErrors.state = "State is required"
    }

    if (!data.zipCode.trim()) {
      newErrors.zipCode = "ZIP code is required"
    } else if (!/^\d{5}(-\d{4})?$/.test(data.zipCode.trim())) {
      newErrors.zipCode = "Please enter a valid ZIP code"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validate()) {
      onNext()
    }
  }

  return (
    <div className="space-y-6 py-4">
      <PhoneInput
        id="phone"
        value={data.phone}
        onChange={(phone) => updateData({ phone })}
        label="Phone Number"
        required={true}
        error={errors.phone}
      />

      <div className="space-y-2">
        <Label htmlFor="email">
          Email <span className="text-red-500">*</span>
        </Label>
        <Input
          id="email"
          type="email"
          value={data.email}
          onChange={(e) => updateData({ email: e.target.value })}
          placeholder="<EMAIL>"
        />
        {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
      </div>

      <div className="space-y-2">
        <Label htmlFor="address">
          Street Address <span className="text-red-500">*</span>
        </Label>
        <Input
          id="address"
          value={data.address}
          onChange={(e) => updateData({ address: e.target.value })}
          placeholder="123 Main St"
        />
        {errors.address && <p className="text-sm text-red-500">{errors.address}</p>}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="city">
            City <span className="text-red-500">*</span>
          </Label>
          <Input
            id="city"
            value={data.city}
            onChange={(e) => updateData({ city: e.target.value })}
            placeholder="Denver"
          />
          {errors.city && <p className="text-sm text-red-500">{errors.city}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="state">
            State <span className="text-red-500">*</span>
          </Label>
          <Input
            id="state"
            value={data.state}
            onChange={(e) => updateData({ state: e.target.value })}
            placeholder="CO"
          />
          {errors.state && <p className="text-sm text-red-500">{errors.state}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="zipCode">
            ZIP Code <span className="text-red-500">*</span>
          </Label>
          <Input
            id="zipCode"
            value={data.zipCode}
            onChange={(e) => updateData({ zipCode: e.target.value })}
            placeholder="80202"
          />
          {errors.zipCode && <p className="text-sm text-red-500">{errors.zipCode}</p>}
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <Label>Service Radius (miles)</Label>
          <div className="flex items-center space-x-4">
            <Slider
              value={[Number.parseInt(data.serviceRadius)]}
              min={1}
              max={100}
              step={1}
              onValueChange={(value) => updateData({ serviceRadius: value[0].toString() })}
              className="flex-1"
            />
            <span className="w-12 text-center">{data.serviceRadius}</span>
          </div>
          <p className="text-xs text-gray-500 mt-1">How far are you willing to travel to provide your services?</p>
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={handleNext}>Next: Review</Button>
      </div>
    </div>
  )
}
