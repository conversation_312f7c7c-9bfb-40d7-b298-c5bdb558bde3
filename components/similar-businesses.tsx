"use client"

import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, MapPin, Phone } from "lucide-react"
import { getSimilarBusinesses } from "@/lib/data"

export function SimilarBusinesses({
  businesses,
  currentBusinessId,
}: {
  businesses: Business[]
  currentBusinessId: string
}) {
  const slicedBusinesses = businesses.slice(0, 3)

  if (slicedBusinesses.length === 0) {
    return (
      <div className="text-center py-6">
        <p className="text-gray-500">No similar businesses found</p>
      </div>
    )
  }

  return (
    <div className="grid gap-4 md:grid-cols-3">
      {slicedBusinesses.map((business) => (
        <Card key={business.id} className="overflow-hidden">
          <CardContent className="p-4">
            <div className="flex items-center gap-3 mb-3">
              <div className="relative h-12 w-12 rounded-md overflow-hidden flex-shrink-0">
                <Image
                  src={business.imageUrl || "/placeholder.svg?height=80&width=80"}
                  alt={business.name}
                  fill
                  className="object-cover"
                />
              </div>
              <div>
                <Link href={`/business/${business.id}`} className="hover:text-blue-600">
                  <h3 className="font-semibold">{business.name}</h3>
                </Link>
                <div className="flex items-center gap-1">
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-3 w-3 ${
                          i < Math.floor(business.rating) ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-xs text-gray-600">
                    {business.rating} ({business.reviewCount})
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center text-xs text-gray-500 mb-2">
              <MapPin className="h-3 w-3 mr-1" />
              {business.address}
            </div>
            {business.is24Hours && (
              <Badge variant="outline" className="bg-green-50 text-green-700 text-xs mb-3">
                Open 24/7
              </Badge>
            )}
            <a
              href={`tel:${business.phone}`}
              className="inline-flex items-center justify-center w-full rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700"
            >
              <Phone className="h-3 w-3 mr-1" />
              Call
            </a>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
