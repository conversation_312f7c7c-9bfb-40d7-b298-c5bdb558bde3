"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"

type DayHours = {
  open: string
  close: string
  isClosed: boolean
}

type HoursData = {
  is24Hours: boolean
  regularHours: {
    monday: DayHours
    tuesday: DayHours
    wednesday: DayHours
    thursday: DayHours
    friday: DayHours
    saturday: DayHours
    sunday: DayHours
  }
  holidayHours: string
  emergencyAfterHours?: boolean
}

interface BusinessHoursFormProps {
  data: HoursData
  updateData: (data: Partial<HoursData>) => void
  onNext: () => void
  onBack: () => void
}

export function BusinessHoursForm({ data, updateData, onNext, onBack }: BusinessHoursFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Update the validate function to enforce the emergency availability requirement
  const validate = () => {
    const newErrors: Record<string, string> = {}

    if (!data.is24Hours) {
      // Validate that at least one day has operating hours if not 24/7
      const hasOperatingHours = Object.values(data.regularHours).some((hours) => !hours.isClosed)

      if (!hasOperatingHours && !data.emergencyAfterHours) {
        newErrors["availability"] =
          "Your business must either be available 24/7, specify operating hours for at least one day of the week, or provide emergency after-hours availability"
      }

      // Validate regular hours if not 24/7
      Object.entries(data.regularHours).forEach(([day, hours]) => {
        if (!hours.isClosed) {
          if (!hours.open) {
            newErrors[`${day}-open`] = "Opening time is required"
          }
          if (!hours.close) {
            newErrors[`${day}-close`] = "Closing time is required"
          }
        }
      })
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validate()) {
      onNext()
    }
  }

  const updateDayHours = (day: string, field: keyof DayHours, value: string | boolean) => {
    updateData({
      regularHours: {
        ...data.regularHours,
        [day]: {
          ...data.regularHours[day as keyof typeof data.regularHours],
          [field]: value,
        },
      },
    })
  }

  // Add an alert message at the top of the component to explain the requirement
  return (
    <div className="space-y-6 py-4">
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
        <h3 className="font-medium text-blue-800 mb-1">Emergency Availability Requirement</h3>
        <p className="text-blue-700 text-sm">
          As an emergency service provider, your business must either be available 24/7 or clearly specify your
          operating hours. This helps customers find available services during emergencies.
        </p>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="is-24-hours"
          checked={data.is24Hours}
          onCheckedChange={(checked) => updateData({ is24Hours: checked })}
        />
        <Label htmlFor="is-24-hours" className="font-medium">
          This business is open 24 hours, 7 days a week
        </Label>
      </div>

      {errors["availability"] && <div className="text-sm text-red-500 mt-2">{errors["availability"]}</div>}

      {!data.is24Hours && (
        <>
          <div className="space-y-4">
            <Label>Regular Business Hours</Label>
            <div className="space-y-4">
              {Object.entries(data.regularHours).map(([day, hours]) => (
                <div key={day} className="grid grid-cols-12 gap-2 items-center">
                  <div className="col-span-3 md:col-span-2">
                    <Label className="capitalize">{day}</Label>
                  </div>
                  <div className="col-span-9 md:col-span-10 grid grid-cols-1 md:grid-cols-12 gap-2 items-center">
                    <div className="md:col-span-4 flex items-center space-x-2">
                      <Checkbox
                        id={`${day}-closed`}
                        checked={hours.isClosed}
                        onCheckedChange={(checked) => {
                          updateDayHours(day, "isClosed", !!checked)
                        }}
                      />
                      <Label htmlFor={`${day}-closed`} className="text-sm">
                        Closed
                      </Label>
                    </div>
                    {!hours.isClosed && (
                      <>
                        <div className="md:col-span-3">
                          <Input
                            type="time"
                            value={hours.open}
                            onChange={(e) => updateDayHours(day, "open", e.target.value)}
                            disabled={hours.isClosed}
                          />
                          {errors[`${day}-open`] && <p className="text-xs text-red-500">{errors[`${day}-open`]}</p>}
                        </div>
                        <div className="md:col-span-1 text-center">to</div>
                        <div className="md:col-span-3">
                          <Input
                            type="time"
                            value={hours.close}
                            onChange={(e) => updateDayHours(day, "close", e.target.value)}
                            disabled={hours.isClosed}
                          />
                          {errors[`${day}-close`] && <p className="text-xs text-red-500">{errors[`${day}-close`]}</p>}
                        </div>
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="border-t pt-4 mt-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="emergency-after-hours"
                checked={data.emergencyAfterHours || false}
                onCheckedChange={(checked) => updateData({ emergencyAfterHours: checked })}
              />
              <Label htmlFor="emergency-after-hours" className="font-medium">
                Available for emergency calls outside of regular business hours
              </Label>
            </div>
            <p className="text-sm text-gray-500 mt-1 ml-7">
              Select this if you can be contacted for emergencies even when your business is officially closed.
            </p>
          </div>
        </>
      )}

      <div className="space-y-2">
        <Label htmlFor="holiday-hours">Holiday Hours (Optional)</Label>
        <Textarea
          id="holiday-hours"
          placeholder="Describe any special hours for holidays or other occasions"
          value={data.holidayHours}
          onChange={(e) => updateData({ holidayHours: e.target.value })}
        />
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={handleNext}>Next: Contact Information</Button>
      </div>
    </div>
  )
}
