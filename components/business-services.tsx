import { Check } from "lucide-react"

// Generate services based on the search term
function generateServices(searchTerm: string) {
  const services = []

  if (searchTerm.includes("notary")) {
    services.push(
      {
        id: "s1",
        name: "Mobile Notary Services",
        description: "We come to your location to notarize documents",
        price: "$50-$100",
        isEmergency: true,
      },
      {
        id: "s2",
        name: "Document Signing",
        description: "Professional document signing services",
        price: "$30-$75",
        isEmergency: true,
      },
      {
        id: "s3",
        name: "Apostille Services",
        description: "Document authentication for international use",
        price: "$75-$150",
        isEmergency: false,
      },
    )
  } else if (searchTerm.includes("locksmith")) {
    services.push(
      {
        id: "s1",
        name: "Emergency Lockout Service",
        description: "Fast response for home, car, or business lockouts",
        price: "$75-$150",
        isEmergency: true,
      },
      {
        id: "s2",
        name: "Lock Repair/Replacement",
        description: "Repair or replace damaged locks",
        price: "$100-$250",
        isEmergency: true,
      },
      {
        id: "s3",
        name: "Key Duplication",
        description: "On-site key duplication services",
        price: "$20-$50",
        isEmergency: false,
      },
    )
  } else if (searchTerm.includes("plumber")) {
    services.push(
      {
        id: "s1",
        name: "Emergency Pipe Repair",
        description: "Fast repair for burst or leaking pipes",
        price: "$150-$300",
        isEmergency: true,
      },
      {
        id: "s2",
        name: "Drain Unclogging",
        description: "Professional drain cleaning and unclogging services",
        price: "$100-$200",
        isEmergency: true,
      },
      {
        id: "s3",
        name: "Water Heater Repair",
        description: "Emergency repair for water heaters",
        price: "$200-$400",
        isEmergency: true,
      },
    )
  } else if (searchTerm.includes("towing")) {
    services.push(
      {
        id: "s1",
        name: "Emergency Towing",
        description: "24/7 towing for cars, trucks, and motorcycles",
        price: "$75-$200",
        isEmergency: true,
      },
      {
        id: "s2",
        name: "Roadside Assistance",
        description: "Jump starts, tire changes, and fuel delivery",
        price: "$50-$100",
        isEmergency: true,
      },
      {
        id: "s3",
        name: "Accident Recovery",
        description: "Recovery services for vehicles after accidents",
        price: "$150-$400",
        isEmergency: true,
      },
    )
  } else if (searchTerm.includes("hvac")) {
    services.push(
      {
        id: "s1",
        name: "Emergency HVAC Repair",
        description: "Fast repair for heating and cooling systems",
        price: "$150-$350",
        isEmergency: true,
      },
      {
        id: "s2",
        name: "Furnace Repair",
        description: "Emergency furnace repair services",
        price: "$200-$400",
        isEmergency: true,
      },
      {
        id: "s3",
        name: "AC Repair",
        description: "Emergency air conditioning repair",
        price: "$150-$300",
        isEmergency: true,
      },
    )
  } else if (searchTerm.includes("electrician")) {
    services.push(
      {
        id: "s1",
        name: "Emergency Electrical Repair",
        description: "Fast response for electrical emergencies",
        price: "$100-$250",
        isEmergency: true,
      },
      {
        id: "s2",
        name: "Circuit Breaker Repair",
        description: "Repair or replace faulty circuit breakers",
        price: "$150-$300",
        isEmergency: true,
      },
      {
        id: "s3",
        name: "Electrical Panel Service",
        description: "Emergency electrical panel repairs",
        price: "$200-$500",
        isEmergency: true,
      },
    )
  } else if (searchTerm.includes("pharmacy")) {
    services.push(
      {
        id: "s1",
        name: "24/7 Prescription Filling",
        description: "Emergency prescription services",
        price: "Varies",
        isEmergency: true,
      },
      {
        id: "s2",
        name: "Medical Supplies",
        description: "Emergency medical supplies and equipment",
        price: "Varies",
        isEmergency: true,
      },
      {
        id: "s3",
        name: "Medication Consultation",
        description: "Emergency medication advice and consultation",
        price: "Free with purchase",
        isEmergency: true,
      },
    )
  } else {
    // Default services for other categories
    services.push(
      {
        id: "s1",
        name: "Emergency Service",
        description: "Fast response for urgent needs",
        price: "$75-$200",
        isEmergency: true,
      },
      {
        id: "s2",
        name: "Standard Service",
        description: "Regular service with appointment",
        price: "$50-$150",
        isEmergency: false,
      },
      {
        id: "s3",
        name: "Consultation",
        description: "Professional advice and consultation",
        price: "$40-$100",
        isEmergency: false,
      },
    )
  }

  return services
}

export function BusinessServices({ businessId, searchTerm }: { businessId: string; searchTerm: string }) {
  // Generate services based on the search term
  const services = generateServices(searchTerm)

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <h3 className="font-bold text-lg mb-6">Services Offered</h3>

      <div className="grid gap-4 md:grid-cols-2">
        {services.map((service) => (
          <div key={service.id} className="border rounded-lg p-4">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold">{service.name}</h4>
                <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                <div className="text-sm font-medium mt-2">
                  Price range: <span className="text-gray-700">{service.price}</span>
                </div>
              </div>
              {service.isEmergency && (
                <div className="bg-green-50 text-green-700 text-xs font-medium px-2 py-1 rounded-full flex items-center">
                  <Check className="h-3 w-3 mr-1" />
                  24/7 Emergency
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
