"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export function SearchLoading() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="animate-spin h-8 w-8 border-4 border-blue-600 border-t-transparent rounded-full"></div>
          </div>
          <h3 className="text-lg font-semibold mb-2">Loading Results...</h3>
          <p className="text-gray-500">Finding the best emergency services for you</p>
        </div>
      </div>
      
      {/* Skeleton loading cards */}
      {Array.from({ length: 3 }).map((_, index) => (
        <Card key={index} className="overflow-hidden">
          <CardContent className="p-0">
            <div className="flex p-4">
              <div className="relative h-20 w-20 rounded-md overflow-hidden flex-shrink-0">
                <Skeleton className="h-full w-full" />
              </div>
              <div className="ml-4 flex-1">
                <Skeleton className="h-6 w-48 mb-2" />
                <Skeleton className="h-4 w-32 mb-2" />
                <div className="flex gap-2 mb-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <Skeleton className="h-4 w-64" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
