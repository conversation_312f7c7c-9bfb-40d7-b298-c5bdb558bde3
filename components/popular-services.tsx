'use client'

import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, MapPin, TrendingUp } from "lucide-react"
import { useEffect, useState } from "react"

type PopularService = {
  searchTerm: string;
  searchCount: number;
  lastSearchedAt: Date;
  sampleBusinesses?: Array<{
    id: string;
    name: string;
    categories: string[];
    phone: string;
    address: string;
    city: string;
    searchTerm: string;
    url: string;
    rating: number;
    reviewCount: number;
    isClosed: boolean;
    latitude: number;
    longitude: number;
    zipCode: string;
    state: string;
    imageUrl: string;
    price: string;
    distance: number;
    is24Hours: boolean;
  }>;
};

export function PopularServices() {
  const [popularServices, setPopularServices] = useState<PopularService[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchPopularServices() {
      try {
        console.log("PopularServices component - starting to fetch data");

        const response = await fetch('/api/popular-services', {
          method: 'GET',
          cache: 'no-store', // Ensure fresh data
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch: ${response.status}`);
        }

        const data = await response.json();
        console.log("PopularServices component - data fetched", {
          serviceCount: data.length,
          serviceSample: data.length > 0 ? `${data[0].searchTerm} (${data[0].searchCount})` : 'none'
        });

        setPopularServices(data);
      } catch (err) {
        console.error("PopularServices component - error fetching data:", err);
        setError(err instanceof Error ? err.message : 'Failed to load popular services');
      } finally {
        setIsLoading(false);
      }
    }

    fetchPopularServices();
  }, []);

  if (isLoading) {
    return (
      <div className="bg-gray-100 rounded-lg p-6 text-center">
        <p className="text-gray-500">Loading popular services...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-100 rounded-lg p-6 text-center">
        <p className="text-gray-500">Unable to load popular services</p>
      </div>
    );
  }
  // If no popular services yet, show a placeholder
  if (popularServices.length === 0) {
    console.log("PopularServices component - no services found");
    return (
      <div className="bg-gray-100 rounded-lg p-6 text-center">
        <p className="text-gray-500">Popular services will appear here as users search</p>
      </div>
    )
  }

  console.log("PopularServices component - rendering with data", { count: popularServices.length });
    
    return (
      <div className="space-y-4">
        {popularServices.map((service) => {
          // Show the most popular business for this search term, or show the search term itself
          const topBusiness = service.sampleBusinesses?.[0]

          if (topBusiness) {
            return (
              <Link key={service.searchTerm} href={`/business/${topBusiness.id}`}>
                <Card className="hover:border-blue-200 hover:shadow-md transition-all">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-lg">{topBusiness.name}</h3>
                          {topBusiness.is24Hours && (
                            <Badge variant="outline" className="bg-green-50 text-green-700 text-xs">
                              24/7
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{topBusiness.categories.join(", ")}</p>
                        <div className="flex items-center gap-2 mb-2">
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-3 w-3 ${
                                  i < Math.floor(topBusiness.rating) ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
                                }`}
                              />
                            ))}
                          </div>
                          <span className="text-xs text-gray-600">
                            {topBusiness.rating} ({topBusiness.reviewCount})
                          </span>
                        </div>
                        <div className="flex items-center text-xs text-gray-500">
                          <MapPin className="h-3 w-3 mr-1" />
                          {topBusiness.address}
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="flex items-center text-blue-600 text-sm font-medium mb-1">
                          <TrendingUp className="h-4 w-4 mr-1" />
                          {service.searchCount} searches
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            )
          } else {
            // Show search term without specific business
            return (
              <Link key={service.searchTerm} href={`/search?q=${encodeURIComponent(service.searchTerm)}`}>
                <Card className="hover:border-blue-200 hover:shadow-md transition-all">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-lg">{service.searchTerm}</h3>
                        <p className="text-sm text-gray-500">Emergency service</p>
                      </div>
                      <div className="flex items-center text-blue-600 text-sm font-medium">
                        <TrendingUp className="h-4 w-4 mr-1" />
                        {service.searchCount} searches
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            )
          }
        })}
      </div>
    )
}
