"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { checkOnboardingStatus } from "@/app/onboarding-actions"

interface OnboardingGuardProps {
  children: React.ReactNode
  requireOnboardingComplete?: boolean
  redirectTo?: string
}

export function OnboardingGuard({
  children,
  requireOnboardingComplete = true,
  redirectTo = "/business/register"
}: OnboardingGuardProps) {
  const router = useRouter()
  const { data: session, status } = useSession()
  const [isLoading, setIsLoading] = useState(true)
  const [hasAccess, setHasAccess] = useState(false)

  useEffect(() => {
    if (status === "loading") return

    if (status === "unauthenticated") {
      router.push("/login")
      return
    }

    if (session?.user?.id) {
      checkOnboardingStatus(session.user.id)
        .then((result) => {
          if (result.error) {
            console.error("Error checking onboarding status:", result.error)
            setHasAccess(false)
            setIsLoading(false)
            return
          }

          // Admin users don't need onboarding
          if (!result.isBusinessUser) {
            setHasAccess(true)
            setIsLoading(false)
            return
          }

          if (requireOnboardingComplete) {
            // User must complete onboarding to access this page
            if (result.needsOnboarding) {
              router.push(redirectTo)
              return
            } else {
              setHasAccess(true)
            }
          } else {
            // User can access this page regardless of onboarding status
            setHasAccess(true)
          }

          setIsLoading(false)
        })
        .catch((error) => {
          console.error("Error checking onboarding status:", error)
          setHasAccess(false)
          setIsLoading(false)
        })
    }
  }, [session, status, router, requireOnboardingComplete, redirectTo])

  if (status === "loading" || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Redirecting...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
