"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { SearchFilters } from "@/components/search-filters"
import { PaginatedSearchResults } from "@/components/paginated-search-results"
import { SearchMap } from "@/components/search-map"
import { Search } from "@/components/search"
import { SearchLoading } from "@/components/search-loading"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { List, MapPin, AlertCircle, ChevronRight, Home } from "lucide-react"
import { searchBusinessesAction } from "@/app/actions"
import { SearchResultsData } from "@/lib/data"

export function SearchPageClient() {
  const [searchData, setSearchData] = useState<SearchResultsData | null>(null)
  const [loading, setLoading] = useState(false)
  const [sortBy, setSortBy] = useState<"rating" | "distance">("rating")

  const searchParams = useSearchParams()

  const query = (searchParams.get("q") as string) || ""
  const location = (searchParams.get("location") as string) || ""
  const category = (searchParams.get("category") as string) || ""
  const page = searchParams.get("page") ? parseInt(searchParams.get("page") as string) : 1
  const pageSize = searchParams.get("pageSize") ? parseInt(searchParams.get("pageSize") as string) : 10

  // Parse filter parameters
  const distance = searchParams.get("distance") ? parseInt(searchParams.get("distance") as string) : undefined
  const open24 = searchParams.get("open24") === "true"
  const verified = searchParams.get("verified") === "true"
  const rating = searchParams.get("rating") ? parseInt(searchParams.get("rating") as string) : undefined

  useEffect(() => {
    async function fetchData() {
      setLoading(true)
      try {
        const data = await searchBusinessesAction({
          query,
          location,
          category,
          page,
          pageSize,
          sortBy,
          filters: {
            distance,
            open24,
            verified,
            rating,
          },
        })
        setSearchData(data)
      } catch (error) {
        console.error("Search error:", error)
        setSearchData(null)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [query, location, category, page, pageSize, sortBy, distance, open24, verified, rating])

  const hasSearchParams = query || location || category

  if (!hasSearchParams) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
              <Link href="/" className="hover:text-blue-600 flex items-center">
                <Home className="h-4 w-4 mr-1" />
                Home
              </Link>
              <ChevronRight className="h-4 w-4" />
              <span>Search</span>
            </nav>

            {/* Search Form */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
              <h1 className="text-2xl font-bold mb-4">Search Emergency Services</h1>
              <Search />
            </div>

            {/* Empty State */}
            <div className="text-center py-12">
              <AlertCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Start Your Search</h2>
              <p className="text-gray-600 mb-6">
                Enter a service type, location, or browse categories to find emergency services near you.
              </p>
              <Link
                href="/"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Browse Categories
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
            <Link href="/" className="hover:text-blue-600 flex items-center">
              <Home className="h-4 w-4 mr-1" />
              Home
            </Link>
            <ChevronRight className="h-4 w-4" />
            <span>Search Results</span>
            {category && (
              <>
                <ChevronRight className="h-4 w-4" />
                <span className="font-medium">{category}</span>
              </>
            )}
          </nav>

          {/* Search Form */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <Search />
          </div>

          {loading ? (
            <SearchLoading />
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Filters Sidebar */}
              <div className="lg:col-span-1">
                <SearchFilters />
              </div>

              {/* Results */}
              <div className="lg:col-span-3">
                <Tabs defaultValue="list" className="w-full">
                  <div className="flex items-center justify-between mb-6">
                    <TabsList className="grid w-fit grid-cols-2">
                      <TabsTrigger value="list" className="flex items-center gap-2">
                        <List className="h-4 w-4" />
                        List
                      </TabsTrigger>
                      <TabsTrigger value="map" className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        Map
                      </TabsTrigger>
                    </TabsList>

                    {searchData && (
                      <div className="text-sm text-gray-600">
                        {searchData.totalCount} results found
                        {location && ` in ${location}`}
                        {category && ` for ${category}`}
                      </div>
                    )}
                  </div>

                  <TabsContent value="list">
                    <PaginatedSearchResults
                      searchData={searchData}
                      sortBy={sortBy}
                      onSortChange={setSortBy}
                    />
                  </TabsContent>

                  <TabsContent value="map">
                    <SearchMap businesses={searchData?.businesses || []} />
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
