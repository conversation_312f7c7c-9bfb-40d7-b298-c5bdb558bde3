"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { OnboardingStepProps } from "@/components/onboarding-wizard"

const DAYS = [
  { key: "monday", label: "Monday" },
  { key: "tuesday", label: "Tuesday" },
  { key: "wednesday", label: "Wednesday" },
  { key: "thursday", label: "Thursday" },
  { key: "friday", label: "Friday" },
  { key: "saturday", label: "Saturday" },
  { key: "sunday", label: "Sunday" },
]

export function HoursStep({ data, updateData, onNext, onBack }: OnboardingStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validate = () => {
    const newErrors: Record<string, string> = {}

    if (!data.hours?.is24Hours) {
      // Validate that at least one day has operating hours if not 24/7
      const hasOperatingHours = Object.values(data.hours?.regularHours || {}).some((hours: any) => !hours.isClosed)

      if (!hasOperatingHours && !data.hours?.emergencyAfterHours) {
        newErrors["availability"] =
          "Your business must either be available 24/7, specify operating hours for at least one day of the week, or provide emergency after-hours availability"
      }

      // Validate regular hours if not 24/7
      Object.entries(data.hours?.regularHours || {}).forEach(([day, hours]: [string, any]) => {
        if (!hours.isClosed) {
          if (!hours.open) {
            newErrors[`${day}-open`] = "Opening time is required"
          }
          if (!hours.close) {
            newErrors[`${day}-close`] = "Closing time is required"
          }
        }
      })
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validate()) {
      onNext()
    }
  }

  const updateHours = (updates: any) => {
    updateData({
      ...data,
      hours: {
        ...data.hours,
        ...updates,
      },
    })
  }

  const updateDayHours = (day: string, updates: any) => {
    updateHours({
      regularHours: {
        ...data.hours?.regularHours,
        [day]: {
          ...data.hours?.regularHours?.[day],
          ...updates,
        },
      },
    })
  }

  const initializeHours = () => {
    if (!data.hours?.regularHours) {
      const defaultHours = DAYS.reduce((acc, day) => {
        acc[day.key] = { open: "09:00", close: "17:00", isClosed: false }
        return acc
      }, {} as any)
      
      updateHours({
        is24Hours: false,
        regularHours: defaultHours,
        holidayHours: "",
        emergencyAfterHours: false,
      })
    }
  }

  // Initialize hours if not set
  if (!data.hours?.regularHours) {
    const defaultHours = DAYS.reduce((acc, day) => {
      acc[day.key] = { open: "09:00", close: "17:00", isClosed: false }
      return acc
    }, {} as any)

    updateHours({
      is24Hours: false,
      regularHours: defaultHours,
      holidayHours: "",
      emergencyAfterHours: false,
    })
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div>
          <Label className="text-base font-medium">Business Hours</Label>
          <p className="text-sm text-gray-500 mt-1">
            Set your operating hours so customers know when you're available
          </p>
        </div>

        {/* 24/7 Toggle */}
        <div className="flex items-center space-x-2 p-4 border rounded-lg">
          <Switch
            id="is-24-hours"
            checked={data.hours?.is24Hours || false}
            onCheckedChange={(checked) => updateHours({ is24Hours: checked })}
          />
          <Label htmlFor="is-24-hours" className="font-medium">
            We operate 24/7
          </Label>
        </div>

        {/* Regular Hours */}
        {!data.hours?.is24Hours && (
          <div className="space-y-4">
            <Label className="text-sm font-medium">Regular Hours</Label>
            <div className="space-y-3">
              {DAYS.map((day) => {
                const dayHours = data.hours?.regularHours?.[day.key] || { open: "09:00", close: "17:00", isClosed: false }
                
                return (
                  <div key={day.key} className="flex items-center gap-4 p-3 border rounded-lg">
                    <div className="w-20">
                      <Label className="text-sm font-medium">{day.label}</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`${day.key}-closed`}
                        checked={dayHours.isClosed}
                        onCheckedChange={(checked) => updateDayHours(day.key, { isClosed: checked })}
                      />
                      <Label htmlFor={`${day.key}-closed`} className="text-sm">Closed</Label>
                    </div>

                    {!dayHours.isClosed && (
                      <div className="flex items-center gap-2">
                        <Input
                          type="time"
                          value={dayHours.open}
                          onChange={(e) => updateDayHours(day.key, { open: e.target.value })}
                          className="w-32"
                        />
                        <span className="text-sm text-gray-500">to</span>
                        <Input
                          type="time"
                          value={dayHours.close}
                          onChange={(e) => updateDayHours(day.key, { close: e.target.value })}
                          className="w-32"
                        />
                      </div>
                    )}

                    {errors[`${day.key}-open`] && (
                      <p className="text-sm text-red-500">{errors[`${day.key}-open`]}</p>
                    )}
                    {errors[`${day.key}-close`] && (
                      <p className="text-sm text-red-500">{errors[`${day.key}-close`]}</p>
                    )}
                  </div>
                )
              })}
            </div>

            {/* Emergency After Hours */}
            <div className="flex items-center space-x-2 p-4 border rounded-lg">
              <Checkbox
                id="emergency-after-hours"
                checked={data.hours?.emergencyAfterHours || false}
                onCheckedChange={(checked) => updateHours({ emergencyAfterHours: checked })}
              />
              <Label htmlFor="emergency-after-hours" className="font-medium">
                Available for emergency calls outside regular hours
              </Label>
            </div>

            {errors.availability && (
              <p className="text-sm text-red-500">{errors.availability}</p>
            )}
          </div>
        )}

        {/* Holiday Hours */}
        <div className="space-y-2">
          <Label htmlFor="holiday-hours">Holiday Hours (Optional)</Label>
          <Textarea
            id="holiday-hours"
            placeholder="Describe any special hours for holidays or other occasions"
            value={data.hours?.holidayHours || ""}
            onChange={(e) => updateHours({ holidayHours: e.target.value })}
            rows={3}
          />
        </div>
      </div>

      <div className="flex justify-between pt-4">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={handleNext}>Next: Contact Information</Button>
      </div>
    </div>
  )
}
