"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle2, Clock, MapPin, Phone, Mail, Globe, Calendar } from "lucide-react"
import { OnboardingStepProps } from "@/components/onboarding-wizard"

export function ReviewStep({ data, onBack, onNext, isSubmitting }: OnboardingStepProps) {
  const formatTime = (time: string) => {
    if (!time) return ""
    try {
      const [hours, minutes] = time.split(":")
      const hour = Number.parseInt(hours)
      const ampm = hour >= 12 ? "PM" : "AM"
      const hour12 = hour % 12 || 12
      return `${hour12}:${minutes} ${ampm}`
    } catch (e) {
      return time
    }
  }

  const formatPhoneNumber = (phone: string) => {
    if (!phone) return ""
    const cleaned = phone.replace(/\D/g, "")
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`
    }
    return phone
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <CheckCircle2 className="w-16 h-16 text-green-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Review Your Business Information</h2>
        <p className="text-gray-600">
          Please review the information below before submitting your business listing for approval.
        </p>
      </div>

      {/* Business Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle2 className="w-5 h-5 text-green-500" />
            Business Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold text-lg">{data.businessInfo?.name}</h3>
            <p className="text-gray-600 mt-1">{data.businessInfo?.description}</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            {data.businessInfo?.yearEstablished && (
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-400" />
                <span>Established: {data.businessInfo.yearEstablished}</span>
              </div>
            )}
            {data.businessInfo?.website && (
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4 text-gray-400" />
                <a href={data.businessInfo.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                  {data.businessInfo.website}
                </a>
              </div>
            )}
            {data.businessInfo?.isEmergencyProvider && (
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-green-500" />
                <span className="text-green-600 font-medium">24/7 Emergency Services</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle2 className="w-5 h-5 text-green-500" />
            Categories & Services
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Primary Category</h4>
            <Badge variant="default" className="text-sm">
              {data.categories?.primaryCategory}
            </Badge>
          </div>
          
          {data.categories?.additionalCategories?.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">Additional Categories</h4>
              <div className="flex flex-wrap gap-2">
                {data.categories.additionalCategories.map((category: string) => (
                  <Badge key={category} variant="secondary" className="text-sm">
                    {category}
                  </Badge>
                ))}
              </div>
            </div>
          )}
          
          {data.categories?.searchTerms?.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">Search Terms</h4>
              <div className="flex flex-wrap gap-2">
                {data.categories.searchTerms.map((term: string) => (
                  <Badge key={term} variant="outline" className="text-sm">
                    {term}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Business Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle2 className="w-5 h-5 text-green-500" />
            Business Hours
          </CardTitle>
        </CardHeader>
        <CardContent>
          {data.hours?.is24Hours ? (
            <div className="flex items-center gap-2 text-green-600 font-medium">
              <Clock className="w-4 h-4" />
              Open 24/7
            </div>
          ) : (
            <div className="space-y-2">
              {Object.entries(data.hours?.regularHours || {}).map(([day, hours]: [string, any]) => (
                <div key={day} className="flex justify-between items-center py-1">
                  <span className="font-medium capitalize">{day}</span>
                  <span className="text-gray-600">
                    {hours.isClosed ? "Closed" : `${formatTime(hours.open)} - ${formatTime(hours.close)}`}
                  </span>
                </div>
              ))}
              
              {data.hours?.emergencyAfterHours && (
                <div className="mt-3 p-2 bg-orange-50 border border-orange-200 rounded text-sm">
                  <span className="text-orange-700 font-medium">Emergency after-hours availability</span>
                </div>
              )}
              
              {data.hours?.holidayHours && (
                <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded text-sm">
                  <span className="text-blue-700 font-medium">Holiday Hours:</span>
                  <p className="text-blue-600 mt-1">{data.hours.holidayHours}</p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle2 className="w-5 h-5 text-green-500" />
            Contact Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4 text-gray-400" />
              <span>{formatPhoneNumber(data.contact?.phone)}</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4 text-gray-400" />
              <span>{data.contact?.email}</span>
            </div>
          </div>
          
          <div className="flex items-start gap-2">
            <MapPin className="w-4 h-4 text-gray-400 mt-1" />
            <div>
              <div>{data.contact?.address}</div>
              <div>{data.contact?.city}, {data.contact?.state} {data.contact?.zipCode}</div>
              <div className="text-sm text-gray-500 mt-1">
                Service radius: {data.contact?.serviceRadius || 25} miles
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submission Notice */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <Clock className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-1">Review Process</h4>
              <p className="text-blue-700 text-sm">
                After submission, your business listing will be reviewed by our team. This typically takes 1-2 business days. 
                You'll receive an email notification once your listing is approved and live on our platform.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between pt-4">
        <Button variant="outline" onClick={onBack} disabled={isSubmitting}>
          Back
        </Button>
        <Button onClick={onNext} disabled={isSubmitting} className="min-w-32">
          {isSubmitting ? "Submitting..." : "Submit for Review"}
        </Button>
      </div>
    </div>
  )
}
