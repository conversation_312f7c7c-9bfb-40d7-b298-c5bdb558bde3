"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"
import { getCategoriesAction } from "@/app/actions"
import { OnboardingStepProps } from "@/components/onboarding-wizard"

export function CategoriesStep({ data, updateData, onNext, onBack }: OnboardingStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [newSearchTerm, setNewSearchTerm] = useState("")
  const [allCategories, setAllCategories] = useState<string[]>([])
  const [isLoadingCategories, setIsLoadingCategories] = useState(true)
  const [categorySearchQuery, setCategorySearchQuery] = useState("")

  const popularCategories = [
    "Emergency Services", "Medical Services", "Legal Services", "Automotive Services",
    "Home Services", "Financial Services", "Technology Services", "Professional Services"
  ]

  useEffect(() => {
    getCategoriesAction()
      .then((categories) => {
        setAllCategories(categories)
        setIsLoadingCategories(false)
      })
      .catch((error) => {
        console.error("Failed to load categories:", error)
        setIsLoadingCategories(false)
      })
  }, [])

  const validate = () => {
    const newErrors: Record<string, string> = {}

    if (!data.categories?.primaryCategory) {
      newErrors.primaryCategory = "Please select a primary category"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validate()) {
      onNext()
    }
  }

  const updateCategories = (updates: any) => {
    updateData({
      ...data,
      categories: {
        ...data.categories,
        ...updates,
      },
    })
  }

  const handleAddSearchTerm = () => {
    if (newSearchTerm.trim() && !data.categories?.searchTerms?.includes(newSearchTerm.trim())) {
      updateCategories({
        searchTerms: [...(data.categories?.searchTerms || []), newSearchTerm.trim()]
      })
      setNewSearchTerm("")
    }
  }

  const removeSearchTerm = (termToRemove: string) => {
    updateCategories({
      searchTerms: data.categories?.searchTerms?.filter((term: string) => term !== termToRemove) || []
    })
  }

  const toggleAdditionalCategory = (category: string) => {
    const currentAdditional = data.categories?.additionalCategories || []
    const isSelected = currentAdditional.includes(category)
    
    if (isSelected) {
      updateCategories({
        additionalCategories: currentAdditional.filter((cat: string) => cat !== category)
      })
    } else {
      updateCategories({
        additionalCategories: [...currentAdditional, category]
      })
    }
  }

  const filteredCategories = allCategories.filter(category =>
    category.toLowerCase().includes(categorySearchQuery.toLowerCase())
  )

  const displayedCategories = categorySearchQuery ? filteredCategories : allCategories.slice(0, 20)

  return (
    <div className="space-y-6">
      {/* Primary Category */}
      <div className="space-y-4">
        <div>
          <Label className="text-base font-medium">
            Primary Category <span className="text-red-500">*</span>
          </Label>
          <p className="text-sm text-gray-500 mt-1">
            Choose the main category that best describes your business
          </p>
        </div>

        {/* Category Search */}
        <div className="space-y-2">
          <Input
            placeholder="Search categories..."
            value={categorySearchQuery}
            onChange={(e) => setCategorySearchQuery(e.target.value)}
          />
        </div>

        {/* Popular Categories */}
        {!categorySearchQuery && (
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Popular Categories</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {popularCategories.map((category) => (
                <Button
                  key={category}
                  variant={data.categories?.primaryCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateCategories({ primaryCategory: category })}
                  className="justify-start text-left h-auto py-2"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* All Categories */}
        <div className="border rounded-lg p-4 max-h-80 overflow-y-auto">
          <RadioGroup
            value={data.categories?.primaryCategory || ""}
            onValueChange={(value) => updateCategories({ primaryCategory: value })}
            className="grid grid-cols-1 md:grid-cols-2 gap-2"
          >
            {displayedCategories.map((category) => (
              <div key={category} className="flex items-center space-x-2">
                <RadioGroupItem value={category} id={`category-${category.toLowerCase().replace(/\s+/g, "-")}`} />
                <Label htmlFor={`category-${category.toLowerCase().replace(/\s+/g, "-")}`} className="text-sm">
                  {category}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        {errors.primaryCategory && <p className="text-sm text-red-500">{errors.primaryCategory}</p>}
      </div>

      {/* Additional Categories */}
      <div className="space-y-4">
        <div>
          <Label className="text-base font-medium">Additional Categories (Optional)</Label>
          <p className="text-sm text-gray-500 mt-1">
            Select any additional categories that apply to your business
          </p>
        </div>

        <div className="border rounded-lg p-4 max-h-60 overflow-y-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {allCategories.slice(0, 30).map((category) => (
              <div key={category} className="flex items-center space-x-2">
                <Checkbox
                  id={`additional-${category.toLowerCase().replace(/\s+/g, "-")}`}
                  checked={data.categories?.additionalCategories?.includes(category) || false}
                  onCheckedChange={() => toggleAdditionalCategory(category)}
                />
                <Label htmlFor={`additional-${category.toLowerCase().replace(/\s+/g, "-")}`} className="text-sm">
                  {category}
                </Label>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Search Terms */}
      <div className="space-y-4">
        <div>
          <Label className="text-base font-medium">Search Terms (Optional)</Label>
          <p className="text-sm text-gray-500 mt-1">
            Add keywords that customers might use to find your business
          </p>
        </div>

        <div className="flex gap-2">
          <Input
            placeholder="Add a search term..."
            value={newSearchTerm}
            onChange={(e) => setNewSearchTerm(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && handleAddSearchTerm()}
          />
          <Button type="button" onClick={handleAddSearchTerm}>Add</Button>
        </div>

        {data.categories?.searchTerms?.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {data.categories.searchTerms.map((term: string) => (
              <Badge key={term} variant="secondary" className="flex items-center gap-1">
                {term}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => removeSearchTerm(term)}
                />
              </Badge>
            ))}
          </div>
        )}
      </div>

      <div className="flex justify-between pt-4">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={handleNext}>Next: Business Hours</Button>
      </div>
    </div>
  )
}
