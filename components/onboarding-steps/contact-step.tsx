"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { PhoneInput } from "@/components/phone-input"
import { OnboardingStepProps } from "@/components/onboarding-wizard"

export function ContactStep({ data, updateData, onNext, onBack }: OnboardingStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validate = () => {
    const newErrors: Record<string, string> = {}

    if (!data.contact?.phone?.trim()) {
      newErrors.phone = "Phone number is required"
    }

    if (!data.contact?.email?.trim()) {
      newErrors.email = "Email address is required"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.contact.email)) {
      newErrors.email = "Please enter a valid email address"
    }

    if (!data.contact?.address?.trim()) {
      newErrors.address = "Street address is required"
    }

    if (!data.contact?.city?.trim()) {
      newErrors.city = "City is required"
    }

    if (!data.contact?.state?.trim()) {
      newErrors.state = "State is required"
    }

    if (!data.contact?.zipCode?.trim()) {
      newErrors.zipCode = "ZIP code is required"
    } else if (!/^\d{5}(-\d{4})?$/.test(data.contact.zipCode)) {
      newErrors.zipCode = "Please enter a valid ZIP code (e.g., 12345 or 12345-6789)"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validate()) {
      onNext()
    }
  }

  const updateContact = (updates: any) => {
    updateData({
      ...data,
      contact: {
        ...data.contact,
        ...updates,
      },
    })
  }

  const serviceRadius = data.contact?.serviceRadius ? parseInt(data.contact.serviceRadius) : 25

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div>
          <Label className="text-base font-medium">Contact Information</Label>
          <p className="text-sm text-gray-500 mt-1">
            Provide your business contact details for customers to reach you
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="phone">
              Phone Number <span className="text-red-500">*</span>
            </Label>
            <PhoneInput
              value={data.contact?.phone || ""}
              onChange={(value) => updateContact({ phone: value })}
              placeholder="(*************"
            />
            {errors.phone && <p className="text-sm text-red-500">{errors.phone}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">
              Email Address <span className="text-red-500">*</span>
            </Label>
            <Input
              id="email"
              type="email"
              value={data.contact?.email || ""}
              onChange={(e) => updateContact({ email: e.target.value })}
              placeholder="<EMAIL>"
            />
            {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="address">
            Street Address <span className="text-red-500">*</span>
          </Label>
          <Input
            id="address"
            value={data.contact?.address || ""}
            onChange={(e) => updateContact({ address: e.target.value })}
            placeholder="123 Main Street"
          />
          {errors.address && <p className="text-sm text-red-500">{errors.address}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="city">
              City <span className="text-red-500">*</span>
            </Label>
            <Input
              id="city"
              value={data.contact?.city || ""}
              onChange={(e) => updateContact({ city: e.target.value })}
              placeholder="Denver"
            />
            {errors.city && <p className="text-sm text-red-500">{errors.city}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="state">
              State <span className="text-red-500">*</span>
            </Label>
            <Input
              id="state"
              value={data.contact?.state || ""}
              onChange={(e) => updateContact({ state: e.target.value })}
              placeholder="CO"
              maxLength={2}
            />
            {errors.state && <p className="text-sm text-red-500">{errors.state}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="zipCode">
              ZIP Code <span className="text-red-500">*</span>
            </Label>
            <Input
              id="zipCode"
              value={data.contact?.zipCode || ""}
              onChange={(e) => updateContact({ zipCode: e.target.value })}
              placeholder="80202"
            />
            {errors.zipCode && <p className="text-sm text-red-500">{errors.zipCode}</p>}
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <Label className="text-base font-medium">Service Area</Label>
            <p className="text-sm text-gray-500 mt-1">
              How far are you willing to travel to serve customers?
            </p>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <Label htmlFor="service-radius">Service Radius</Label>
                <span className="text-sm text-gray-500">{serviceRadius} miles</span>
              </div>
              <Slider
                id="service-radius"
                min={1}
                max={100}
                step={1}
                value={[serviceRadius]}
                onValueChange={(value) => updateContact({ serviceRadius: value[0].toString() })}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-400">
                <span>1 mile</span>
                <span>100 miles</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between pt-4">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={handleNext}>Next: Review & Submit</Button>
      </div>
    </div>
  )
}
