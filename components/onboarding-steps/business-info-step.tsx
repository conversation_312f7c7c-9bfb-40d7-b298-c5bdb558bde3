"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { BusinessImageUpload } from "@/components/business-image-upload"
import { OnboardingStepProps } from "@/components/onboarding-wizard"

export function BusinessInfoStep({ data, updateData, onNext }: OnboardingStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validate = () => {
    const newErrors: Record<string, string> = {}

    if (!data.businessInfo?.name?.trim()) {
      newErrors.name = "Business name is required"
    }

    if (!data.businessInfo?.description?.trim()) {
      newErrors.description = "Business description is required"
    } else if (data.businessInfo.description.length < 50) {
      newErrors.description = "Description must be at least 50 characters"
    }

    if (data.businessInfo?.yearEstablished) {
      const year = Number.parseInt(data.businessInfo.yearEstablished)
      const currentYear = new Date().getFullYear()
      if (isNaN(year) || year < 1900 || year > currentYear) {
        newErrors.yearEstablished = `Year must be between 1900 and ${currentYear}`
      }
    }

    if (data.businessInfo?.website && !/^(https?:\/\/)?(www\.)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(\/.*)?$/.test(data.businessInfo.website)) {
      newErrors.website = "Please enter a valid website URL"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validate()) {
      onNext()
    }
  }

  const updateBusinessInfo = (updates: any) => {
    updateData({
      ...data,
      businessInfo: {
        ...data.businessInfo,
        ...updates,
      },
    })
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="business-name">
          Business Name <span className="text-red-500">*</span>
        </Label>
        <Input
          id="business-name"
          value={data.businessInfo?.name || ""}
          onChange={(e) => updateBusinessInfo({ name: e.target.value })}
          placeholder="Enter your business name"
        />
        {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">
          Business Description <span className="text-red-500">*</span>
        </Label>
        <Textarea
          id="description"
          value={data.businessInfo?.description || ""}
          onChange={(e) => updateBusinessInfo({ description: e.target.value })}
          placeholder="Describe your business and the services you provide (minimum 50 characters)"
          rows={4}
        />
        <div className="flex justify-between text-sm text-gray-500">
          <span>{errors.description || "Minimum 50 characters required"}</span>
          <span>{data.businessInfo?.description?.length || 0} characters</span>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="year-established">Year Established</Label>
        <Input
          id="year-established"
          type="number"
          value={data.businessInfo?.yearEstablished || ""}
          onChange={(e) => updateBusinessInfo({ yearEstablished: e.target.value })}
          placeholder="e.g., 2020"
          min="1900"
          max={new Date().getFullYear()}
        />
        {errors.yearEstablished && <p className="text-sm text-red-500">{errors.yearEstablished}</p>}
      </div>

      <div className="space-y-2">
        <Label htmlFor="website">Website</Label>
        <Input
          id="website"
          value={data.businessInfo?.website || ""}
          onChange={(e) => updateBusinessInfo({ website: e.target.value })}
          placeholder="e.g., https://www.yourbusiness.com"
        />
        {errors.website && <p className="text-sm text-red-500">{errors.website}</p>}
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="emergency-provider"
          checked={data.businessInfo?.isEmergencyProvider || false}
          onCheckedChange={(checked) => updateBusinessInfo({ isEmergencyProvider: checked })}
        />
        <Label htmlFor="emergency-provider">This business provides 24/7 emergency services</Label>
      </div>

      <div className="space-y-2">
        <Label>Business Image</Label>
        <BusinessImageUpload
          currentImageUrl={data.businessInfo?.imageUrl}
          onImageUploaded={(imageUrl) => updateBusinessInfo({ imageUrl })}
        />
      </div>

      <div className="flex justify-end pt-4">
        <Button onClick={handleNext}>Next: Categories</Button>
      </div>
    </div>
  )
}
