"use client"

import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, MapPin, Phone } from "lucide-react"
import { type Business } from "@/lib/data"
import { ClearFiltersButton } from "@/components/clear-filters-button"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { usePathname, useSearchParams } from 'next/navigation'

export function SearchResults({
  businesses,
  totalCount,
  currentPage,
  pageSize,
  query = "",
  location = "",
  category = "",
  onPageChange,
}: {
  businesses: Business[]
  totalCount: number
  currentPage: number
  pageSize: number
  query?: string
  location?: string
  category?: string
  onPageChange?: (page: number) => void
}) {
  const totalPages = Math.ceil(totalCount / pageSize);

  if (businesses.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold mb-2">No results found</h3>
        <p className="text-gray-500 mb-6">Try adjusting your search or filters</p>
        <ClearFiltersButton />
      </div>
    )
  }

  const handlePageChange = (page: number) => {
    if (onPageChange) {
      onPageChange(page)
    }
  }

  return (
    <div className="space-y-4">
      {businesses.map((business) => (
        <Card key={business.id} className="overflow-hidden">
          <CardContent className="p-0">
            <div className="flex p-4">
              <Link href={`/business/${business.id}`} className="flex flex-1 hover:bg-gray-50">
                <div className="relative h-20 w-20 rounded-md overflow-hidden flex-shrink-0">
                  <Image
                    src={business.imageUrl || "/placeholder_4540173.png"} // Use the new placeholder
                    alt={business.name}
                    fill
                    className="object-cover"
                    onError={(e) => {
                      e.currentTarget.src = "/placeholder_4540173.png"; // Set new placeholder on error
                    }}
                  />
                </div>
                <div className="ml-4 flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold">{business.name}</h3>
                    {business.is24Hours && (
                      <Badge variant="outline" className="bg-green-50 text-green-700 text-xs">
                        24/7
                      </Badge>
                    )}
                  </div>
                  <div className="text-sm text-gray-500 mb-1">{business.categories.join(", ")}</div>
                  <div className="flex items-center gap-1 mb-2">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-3 w-3 ${
                            i < Math.floor(business.rating) ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-xs text-gray-600">
                      {business.rating} ({business.reviewCount})
                    </span>
                  </div>
                  <div className="flex items-center text-xs text-gray-500 mb-1">
                    <MapPin className="h-3 w-3 mr-1" />
                    {business.address}
                  </div>
                  {business.price && <div className="text-xs text-gray-500">Price: {business.price}</div>}
                </div>
              </Link>
              <div className="ml-auto flex flex-col items-end justify-between">
                <a
                  href={`tel:${business.phone}`}
                  className="inline-flex items-center justify-center rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700"
                >
                  <Phone className="h-3 w-3 mr-1" />
                  Call
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => handlePageChange(currentPage > 1 ? currentPage - 1 : 1)}
                isActive={currentPage > 1}
              />
            </PaginationItem>
            {/* Compact pagination with ellipsis and page ranges */}
            {(() => {
              const maxPagesToShow = 10;
              const pageGroups = Math.ceil(totalPages / maxPagesToShow);
              const currentGroup = Math.floor((currentPage - 1) / maxPagesToShow);

              const startPage = currentGroup * maxPagesToShow + 1;
              const endPage = Math.min(startPage + maxPagesToShow - 1, totalPages);

              const pages = [];
              for (let i = startPage; i <= endPage; i++) {
                pages.push(i);
              }

              return (
                <>
              {/* Show ellipsis if there are more pages before current group */}
              {currentGroup > 0 && (
                <PaginationItem>
                  <PaginationLink
                    onClick={() => handlePageChange((currentGroup - 1) * maxPagesToShow + 1)}
                    className="cursor-pointer select-none"
                  >
                    ...
                  </PaginationLink>
                </PaginationItem>
              )}

              {pages.map((pageNum) => (
                <PaginationItem key={pageNum}>
                  <PaginationLink
                    onClick={() => handlePageChange(pageNum)}
                    isActive={currentPage === pageNum}
                  >
                    {pageNum}
                  </PaginationLink>
                </PaginationItem>
              ))}

              {/* Show ellipsis if there are more pages after current group */}
              {currentGroup < pageGroups - 1 && (
                <PaginationItem>
                  <PaginationLink
                    onClick={() => handlePageChange((currentGroup + 1) * maxPagesToShow + 1)}
                    className="cursor-pointer select-none"
                  >
                    ...
                  </PaginationLink>
                </PaginationItem>
              )}
                </>
              );
            })()}
            <PaginationItem>
              <PaginationNext
                onClick={() => handlePageChange(currentPage < totalPages ? currentPage + 1 : totalPages)}
                isActive={currentPage < totalPages}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  )
}
