import Link from "next/link"
import { FileText, Key, Truck, Zap, Thermometer, Stethoscope, ShieldAlert, Wrench, Fingerprint } from "lucide-react"
import { getAllCategories } from "@/lib/db-access"
import { cache } from "react"

// Map category names to icons
const categoryIcons: Record<string, any> = {
  Notaries: FileText,
  "Translation Services": FileText,
  Fingerprinting: Fingerprint,
  Locksmiths: Key,
  "Security Services": ShieldAlert,
  Towing: Truck,
  "Roadside Assistance": Truck,
  "Auto Repair": Wrench,
  Electricians: Zap,
  Plumbers: Wrench,
  "Emergency Services": ShieldAlert,
  "Water Damage Restoration": Wrench,
  "Heating & Air Conditioning/HVAC": Thermometer,
  Pharmacies: Stethoscope,
  "Health & Medical": Stethoscope,
  "Legal Services": FileText,
}

// Prioritize emergency-related categories
const priorityCategories = [
  "Notaries",
  "Locksmiths",
  "Towing",
  "Plumbers",
  "Electricians",
  "Emergency Services",
  "Legal Services",
  "Heating & Air Conditioning/HVAC",
  "Pharmacies",
]

// Categories to exclude from display (less relevant for emergency services)
const excludedCategories = [
  "Acai Bowls",
  "3D Printing",
  "Wills, Trusts, & Probates", // Use "Legal Services" instead for broader appeal
]

// Cache the getAllCategories call to optimize duplicate calls
const cachedGetAllCategories = cache(getAllCategories)

// Get a subset of categories to display
async function getDisplayCategories() {
  const allCategories = await cachedGetAllCategories()

  // Filter to get priority categories that exist in our data
  const displayCategories = priorityCategories.filter((cat) => allCategories.includes(cat))

  // Add other categories if we need more, but exclude unwanted categories
  if (displayCategories.length < 8) {
    allCategories.forEach((cat) => {
      if (!displayCategories.includes(cat) &&
          !excludedCategories.includes(cat) &&
          displayCategories.length < 8) {
        displayCategories.push(cat)
      }
    })
  }

  return displayCategories
}

export async function ServiceCategories() {
  const categories = await getDisplayCategories()

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {categories.map((category) => {
        const IconComponent = categoryIcons[category] || ShieldAlert

        return (
          <Link
            key={category}
            href={`/search?category=${encodeURIComponent(category)}`}
            className="bg-white rounded-lg border p-4 hover:border-blue-500 hover:shadow-md transition-all"
          >
            <div className="flex flex-col items-center justify-center text-center h-full">
              <div className="bg-blue-100 p-3 rounded-full mb-3">
                <IconComponent className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold mb-1">{category}</h3>
              <p className="text-sm text-gray-500">24/7 {category.toLowerCase()} services</p>
            </div>
          </Link>
        )
      })}
    </div>
  )
}
