"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle2, Clock, MapPin, Phone, Mail, Globe } from "lucide-react"

interface BusinessReviewFormProps {
  formData: {
    businessInfo: {
      name: string
      description: string
      yearEstablished: string
      website: string
      isEmergencyProvider: boolean
    }
    categories: {
      primaryCategory: string
      additionalCategories: string[]
      searchTerms: string[]
    }
    hours: {
      is24Hours: boolean
      regularHours: Record<string, { open: string; close: string; isClosed: boolean }>
      holidayHours: string
      emergencyAfterHours?: boolean
    }
    contact: {
      phone: string
      email: string
      address: string
      city: string
      state: string
      zipCode: string
      serviceRadius: string
    }
  }
  onBack: () => void
  onSubmit: () => void
  isSubmitting: boolean
}

export function BusinessReviewForm({ formData, onBack, onSubmit, isSubmitting }: BusinessReviewFormProps) {
  const formatTime = (time: string) => {
    if (!time) return ""
    try {
      const [hours, minutes] = time.split(":")
      const hour = Number.parseInt(hours)
      const ampm = hour >= 12 ? "PM" : "AM"
      const hour12 = hour % 12 || 12
      return `${hour12}:${minutes} ${ampm}`
    } catch (e) {
      return time
    }
  }

  return (
    <div className="space-y-6 py-4">
      <h3 className="text-lg font-medium">Review Your Business Information</h3>
      <p className="text-sm text-gray-500">
        Please review the information below before submitting your business listing.
      </p>

      <div className="space-y-6">
        <Card>
          <CardContent className="p-4">
            <h4 className="font-semibold text-lg mb-2">{formData.businessInfo.name}</h4>
            <p className="text-gray-600 mb-4">{formData.businessInfo.description}</p>

            <div className="flex flex-wrap gap-2 mb-4">
              <Badge variant="secondary">{formData.categories.primaryCategory}</Badge>
              {formData.categories.additionalCategories.map((category) => (
                <Badge key={category} variant="outline">
                  {category}
                </Badge>
              ))}
            </div>

            {formData.businessInfo.isEmergencyProvider && (
              <div className="flex items-center text-green-600 mb-4">
                <CheckCircle2 className="h-4 w-4 mr-2" />
                <span className="text-sm font-medium">Provides Emergency Services</span>
              </div>
            )}

            <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
              <h5 className="font-medium text-blue-800 mb-1">Emergency Availability</h5>
              {formData.hours.is24Hours ? (
                <p className="text-blue-700 text-sm">Available 24 hours, 7 days a week</p>
              ) : (
                <>
                  <p className="text-blue-700 text-sm">Available during specified business hours</p>
                  {formData.hours.emergencyAfterHours && (
                    <p className="text-blue-700 text-sm mt-1">
                      <CheckCircle2 className="h-3 w-3 inline mr-1" />
                      Also available for emergency calls outside of regular business hours
                    </p>
                  )}
                </>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h5 className="font-medium mb-2">Business Details</h5>
                <ul className="space-y-2 text-sm">
                  {formData.businessInfo.yearEstablished && (
                    <li>Established: {formData.businessInfo.yearEstablished}</li>
                  )}
                  {formData.businessInfo.website && (
                    <li className="flex items-center">
                      <Globe className="h-4 w-4 mr-2 text-gray-500" />
                      <a href={formData.businessInfo.website} className="text-blue-600 hover:underline">
                        {formData.businessInfo.website}
                      </a>
                    </li>
                  )}
                </ul>
              </div>

              <div>
                <h5 className="font-medium mb-2">Hours</h5>
                {formData.hours.is24Hours ? (
                  <p className="text-sm">Open 24 hours, 7 days a week</p>
                ) : (
                  <ul className="space-y-1 text-sm">
                    {Object.entries(formData.hours.regularHours).map(([day, hours]) => (
                      <li key={day} className="flex items-start">
                        <Clock className="h-4 w-4 mr-2 text-gray-500 mt-0.5" />
                        <span className="capitalize w-20">{day}:</span>
                        <span>
                          {hours.isClosed ? "Closed" : `${formatTime(hours.open)} - ${formatTime(hours.close)}`}
                        </span>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>

            <div className="mt-4">
              <h5 className="font-medium mb-2">Contact Information</h5>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-gray-500" />
                  {formData.contact.phone}
                </li>
                <li className="flex items-center">
                  <Mail className="h-4 w-4 mr-2 text-gray-500" />
                  {formData.contact.email}
                </li>
                <li className="flex items-start">
                  <MapPin className="h-4 w-4 mr-2 text-gray-500 mt-0.5" />
                  <div>
                    <div>{formData.contact.address}</div>
                    <div>
                      {formData.contact.city}, {formData.contact.state} {formData.contact.zipCode}
                    </div>
                  </div>
                </li>
              </ul>
            </div>

            <div className="mt-4">
              <h5 className="font-medium mb-2">Service Area</h5>
              <p className="text-sm">
                Serves customers within {formData.contact.serviceRadius} miles of business location
              </p>
            </div>

            {formData.categories.searchTerms.length > 0 && (
              <div className="mt-4">
                <h5 className="font-medium mb-2">Search Terms</h5>
                <div className="flex flex-wrap gap-2">
                  {formData.categories.searchTerms.map((term) => (
                    <Badge key={term} variant="outline">
                      {term}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={onSubmit} disabled={isSubmitting}>
          {isSubmitting ? "Submitting..." : "Submit Business"}
        </Button>
      </div>
    </div>
  )
}
