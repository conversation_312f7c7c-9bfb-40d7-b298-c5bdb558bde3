"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ModeToggle } from "@/components/mode-toggle"
import { Phone<PERSON><PERSON>, Menu } from "lucide-react"
import { Sheet, SheetContent, SheetDescription, SheetHeader, Sheet<PERSON>itle, SheetTrigger } from "@/components/ui/sheet"
import { UserAccountMenu } from "@/components/user-account-menu"
import { useSession, signOut } from "next-auth/react"

export function Header() {
  const { data: session } = useSession()
  return (
    <header className="border-b bg-white dark:bg-gray-950">
      <div className="container mx-auto max-w-6xl px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-8">
            <Link href="/" className="font-bold text-2xl text-blue-600">
              EmergencyFind
            </Link>
            <nav className="hidden md:flex items-center gap-6">
              <Link href="/categories" className="text-gray-600 hover:text-blue-600 font-medium">
                Categories
              </Link>
              <Link href="/locations" className="text-gray-600 hover:text-blue-600 font-medium">
                Locations
              </Link>
              <Link href="/guides" className="text-gray-600 hover:text-blue-600 font-medium">
                Emergency Guides
              </Link>
              <Link href="/about" className="text-gray-600 hover:text-blue-600 font-medium">
                About
              </Link>
            </nav>
          </div>
          <div className="flex items-center gap-4">
            <div className="hidden md:flex items-center gap-4">
              {!session ? (
                <Link href="/business/register">
                  <Button variant="outline">List Your Business</Button>
                </Link>
              ) : (
                <Link href="/business/dashboard">
                  <Button variant="outline">Business Dashboard</Button>
                </Link>
              )}
              <UserAccountMenu />
              <ModeToggle />
            </div>
            <div className="md:hidden flex items-center gap-4">
              <Button size="icon" variant="ghost" className="text-blue-600">
                <PhoneCall className="h-5 w-5" />
              </Button>
              <Sheet>
                <SheetTrigger asChild>
                  <Button size="icon" variant="ghost">
                    <Menu className="h-5 w-5" />
                  </Button>
                </SheetTrigger>
                <SheetContent>
                  <SheetHeader>
                    <SheetTitle>EmergencyFind</SheetTitle>
                    <SheetDescription>Find emergency services near you 24/7</SheetDescription>
                  </SheetHeader>
                  <div className="flex flex-col gap-4 py-4">
                    <Link href="/categories" className="text-gray-600 hover:text-blue-600 font-medium">
                      Categories
                    </Link>
                    <Link href="/locations" className="text-gray-600 hover:text-blue-600 font-medium">
                      Locations
                    </Link>
                    <Link href="/guides" className="text-gray-600 hover:text-blue-600 font-medium">
                      Emergency Guides
                    </Link>
                    <Link href="/about" className="text-gray-600 hover:text-blue-600 font-medium">
                      About
                    </Link>
                    <hr className="my-2" />
                    {!session ? (
                      <Link href="/business/register">
                        <Button variant="outline" className="w-full">
                          List Your Business
                        </Button>
                      </Link>
                    ) : (
                      <Link href="/business/dashboard">
                        <Button variant="outline" className="w-full">
                          Business Dashboard
                        </Button>
                      </Link>
                    )}
                    {!session ? (
                      <>
                        <Link href="/login">
                          <Button variant="ghost" className="w-full">
                            Login
                          </Button>
                        </Link>
                        <Link href="/signup">
                          <Button className="w-full">Sign Up</Button>
                        </Link>
                      </>
                    ) : (
                      <Button
                        variant="ghost"
                        className="w-full"
                        onClick={() => signOut({ callbackUrl: "/" })}
                      >
                        Log Out
                      </Button>
                    )}
                    <ModeToggle />
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
