"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"
import { getCategoriesAction } from "@/app/actions"

type CategoriesData = {
  primaryCategory: string
  additionalCategories: string[]
  searchTerms: string[]
}

interface BusinessCategoriesFormProps {
  data: CategoriesData
  updateData: (data: Partial<CategoriesData>) => void
  onNext: () => void
  onBack: () => void
}

export function BusinessCategoriesForm({ data, updateData, onNext, onBack }: BusinessCategoriesFormProps) {
  const [errors, setErrors] = useState<Partial<Record<keyof CategoriesData, string>>>({})
  const [newSearchTerm, setNewSearchTerm] = useState("")
  const [allCategories, setAllCategories] = useState<string[]>([])
  const [isLoadingCategories, setIsLoadingCategories] = useState(true)
  const [categorySearchQuery, setCategorySearchQuery] = useState("")
  const [showAllCategories, setShowAllCategories] = useState(false)
  const [additionalCategorySearchQuery, setAdditionalCategorySearchQuery] = useState("")
  const [showAllAdditionalCategories, setShowAllAdditionalCategories] = useState(false)

  // Fetch all available categories
  useEffect(() => {
    async function fetchCategories() {
      try {
        const categories = await getCategoriesAction()
        setAllCategories(categories)
      } catch (error) {
        console.error('Error fetching categories:', error)
        setAllCategories([])
      } finally {
        setIsLoadingCategories(false)
      }
    }

    fetchCategories()
  }, [])

  // Filter categories based on search query for primary category
  const filteredCategories = allCategories.filter(category =>
    category.toLowerCase().includes(categorySearchQuery.toLowerCase())
  )

  // Show only first 20 categories by default, or all if searching/showing all
  const displayedCategories = categorySearchQuery || showAllCategories
    ? filteredCategories
    : filteredCategories.slice(0, 20)

  // Filter categories for additional categories section
  const filteredAdditionalCategories = allCategories.filter(category =>
    category.toLowerCase().includes(additionalCategorySearchQuery.toLowerCase()) &&
    category !== data.primaryCategory
  )

  // Show only first 20 additional categories by default, or all if searching/showing all
  const displayedAdditionalCategories = additionalCategorySearchQuery || showAllAdditionalCategories
    ? filteredAdditionalCategories
    : filteredAdditionalCategories.slice(0, 20)

  // Popular category suggestions for better UX
  const popularCategories = [
    "Plumbers", "Electricians", "Locksmiths", "Restaurants", "Auto Repair",
    "Lawyers", "Dentists", "Hair Salons", "Contractors", "Cleaning Services"
  ]

  const getPopularCategoriesInList = () => {
    return popularCategories.filter(cat =>
      allCategories.some(category =>
        category.toLowerCase().includes(cat.toLowerCase())
      )
    ).slice(0, 6)
  }

  const validate = () => {
    const newErrors: Partial<Record<keyof CategoriesData, string>> = {}

    if (!data.primaryCategory) {
      newErrors.primaryCategory = "Please select a primary category"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validate()) {
      onNext()
    }
  }

  const handleAddSearchTerm = () => {
    if (newSearchTerm.trim() && !data.searchTerms.includes(newSearchTerm.trim())) {
      updateData({ searchTerms: [...data.searchTerms, newSearchTerm.trim()] })
      setNewSearchTerm("")
    }
  }

  const handleRemoveSearchTerm = (term: string) => {
    updateData({ searchTerms: data.searchTerms.filter((t) => t !== term) })
  }

  const toggleAdditionalCategory = (category: string) => {
    if (data.additionalCategories.includes(category)) {
      updateData({ additionalCategories: data.additionalCategories.filter((c) => c !== category) })
    } else {
      updateData({ additionalCategories: [...data.additionalCategories, category] })
    }
  }

  if (isLoadingCategories) {
    return (
      <div className="space-y-6 py-4">
        <div className="text-center py-8">
          <p className="text-gray-500">Loading categories...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 py-4">
      <div className="space-y-4">
        <Label>
          Primary Category <span className="text-red-500">*</span>
        </Label>
        <p className="text-sm text-gray-500">
          Select the main category that best describes your business
        </p>
        {errors.primaryCategory && (
          <p className="text-sm text-red-600">{errors.primaryCategory}</p>
        )}

        {/* Category Search */}
        <div className="space-y-2">
          <div className="relative">
            <Input
              placeholder="Search categories... (e.g., plumber, restaurant, lawyer)"
              value={categorySearchQuery}
              onChange={(e) => setCategorySearchQuery(e.target.value)}
              className="w-full pr-10"
            />
            {categorySearchQuery && (
              <button
                type="button"
                onClick={() => setCategorySearchQuery("")}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
          {categorySearchQuery && (
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-500">
                Found {filteredCategories.length} categories matching "{categorySearchQuery}"
              </p>
              {filteredCategories.length > 0 && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setCategorySearchQuery("")}
                  className="text-xs"
                >
                  Clear search
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Popular Categories (shown when not searching) */}
        {!categorySearchQuery && (
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700">Popular categories:</p>
            <div className="flex flex-wrap gap-2">
              {getPopularCategoriesInList().map((category) => (
                <Button
                  key={category}
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const fullCategory = allCategories.find(cat =>
                      cat.toLowerCase().includes(category.toLowerCase())
                    )
                    if (fullCategory) {
                      updateData({ primaryCategory: fullCategory })
                    }
                  }}
                  className="text-xs"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Category Selection */}
        <div className="border rounded-lg p-4 max-h-80 overflow-y-auto">
          <RadioGroup
            value={data.primaryCategory}
            onValueChange={(value) => updateData({ primaryCategory: value })}
            className="grid grid-cols-1 md:grid-cols-2 gap-2"
          >
            {displayedCategories.map((category) => (
              <div key={category} className="flex items-center space-x-2">
                <RadioGroupItem value={category} id={`category-${category.toLowerCase().replace(/\s+/g, "-")}`} />
                <Label htmlFor={`category-${category.toLowerCase().replace(/\s+/g, "-")}`} className="text-sm">
                  {category}
                </Label>
              </div>
            ))}
          </RadioGroup>

          {/* Show more button */}
          {!categorySearchQuery && !showAllCategories && filteredCategories.length > 20 && (
            <div className="mt-4 text-center">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowAllCategories(true)}
              >
                Show all {filteredCategories.length} categories
              </Button>
            </div>
          )}

          {/* No results message */}
          {categorySearchQuery && filteredCategories.length === 0 && (
            <div className="text-center py-4 text-gray-500">
              <p>No categories found matching "{categorySearchQuery}"</p>
              <p className="text-sm">Try a different search term or browse all categories</p>
            </div>
          )}
        </div>
      </div>

      <div className="space-y-4">
        <Label>Additional Categories (Optional)</Label>
        <p className="text-sm text-gray-500">
          Select any additional categories that also describe your business
        </p>

        {/* Additional Category Search */}
        <div className="space-y-2">
          <div className="relative">
            <Input
              placeholder="Search additional categories..."
              value={additionalCategorySearchQuery}
              onChange={(e) => setAdditionalCategorySearchQuery(e.target.value)}
              className="w-full pr-10"
            />
            {additionalCategorySearchQuery && (
              <button
                type="button"
                onClick={() => setAdditionalCategorySearchQuery("")}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
          {additionalCategorySearchQuery && (
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-500">
                Found {filteredAdditionalCategories.length} additional categories matching "{additionalCategorySearchQuery}"
              </p>
              {filteredAdditionalCategories.length > 0 && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setAdditionalCategorySearchQuery("")}
                  className="text-xs"
                >
                  Clear search
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Additional Categories Selection */}
        <div className="border rounded-lg p-4 max-h-60 overflow-y-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {displayedAdditionalCategories.map((category) => (
                <div key={category} className="flex items-center space-x-2">
                  <Checkbox
                    id={`additional-${category.toLowerCase().replace(/\s+/g, "-")}`}
                    checked={data.additionalCategories.includes(category)}
                    onCheckedChange={() => toggleAdditionalCategory(category)}
                  />
                  <Label htmlFor={`additional-${category.toLowerCase().replace(/\s+/g, "-")}`} className="text-sm">
                    {category}
                  </Label>
                </div>
              ))}
          </div>

          {/* Show more button for additional categories */}
          {!additionalCategorySearchQuery && !showAllAdditionalCategories && filteredAdditionalCategories.length > 20 && (
            <div className="mt-4 text-center">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowAllAdditionalCategories(true)}
              >
                Show all {filteredAdditionalCategories.length} additional categories
              </Button>
            </div>
          )}

          {/* No results message for additional categories */}
          {additionalCategorySearchQuery && filteredAdditionalCategories.length === 0 && (
            <div className="text-center py-4 text-gray-500">
              <p>No additional categories found matching "{additionalCategorySearchQuery}"</p>
              <p className="text-sm">Try a different search term</p>
            </div>
          )}

          {/* Show selected additional categories */}
          {data.additionalCategories.length > 0 && (
            <div className="mt-4 pt-4 border-t">
              <p className="text-sm font-medium text-gray-700 mb-2">Selected additional categories:</p>
              <div className="flex flex-wrap gap-2">
                {data.additionalCategories.map((category) => (
                  <Badge key={category} variant="secondary" className="text-xs">
                    {category}
                    <button
                      type="button"
                      onClick={() => toggleAdditionalCategory(category)}
                      className="ml-1 hover:text-red-600"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="space-y-4">
        <Label>Search Terms (Optional)</Label>
        <p className="text-sm text-gray-500">
          Add keywords that customers might use to find your business (e.g., &quot;24 hour plumber&quot;,
          &quot;emergency locksmith&quot;)
        </p>

        <div className="flex space-x-2">
          <Input
            placeholder="Add a search term"
            value={newSearchTerm}
            onChange={(e) => setNewSearchTerm(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.preventDefault()
                handleAddSearchTerm()
              }
            }}
          />
          <Button type="button" onClick={handleAddSearchTerm}>
            Add
          </Button>
        </div>

        <div className="flex flex-wrap gap-2 mt-2">
          {data.searchTerms.map((term) => (
            <Badge key={term} variant="secondary" className="px-3 py-1">
              {term}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1 p-0"
                onClick={() => handleRemoveSearchTerm(term)}
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove</span>
              </Button>
            </Badge>
          ))}
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={handleNext}>Next: Hours</Button>
      </div>
    </div>
  )
}
