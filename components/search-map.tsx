import { type Business } from "@/lib/data"
import { searchBusinessesAction } from "@/app/actions"
import { SearchMapClient } from "./search-map-client"

export async function SearchMap({
  query = "",
  location = "",
  category = "",
  distance,
  open24,
  verified,
  rating,
}: {
  query?: string
  location?: string
  category?: string
  distance?: number
  open24?: boolean
  verified?: boolean
  rating?: number
}) {
  // Fetch businesses on the server
  const { businesses, locationInfo } = await searchBusinessesAction({
    query: query || undefined,
    location: location || undefined,
    category: category || undefined,
    distance,
    open24,
    verified,
    rating,
    sortBy: "rating",
  })

  // Pass the data to the client component
  return (
    <SearchMapClient
      businesses={businesses}
      query={query}
      location={location}
      category={category}
    />
  )
}
