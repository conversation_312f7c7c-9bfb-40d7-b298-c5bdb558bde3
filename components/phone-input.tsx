"use client"

import { useState, useRef, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"

interface PhoneInputProps {
  value: string
  onChange: (value: string) => void
  label?: string
  placeholder?: string
  required?: boolean
  error?: string
  className?: string
  id?: string
}

export function PhoneInput({
  value,
  onChange,
  label = "Phone Number",
  placeholder = "(*************",
  required = false,
  error,
  className,
  id
}: PhoneInputProps) {
  const [displayValue, setDisplayValue] = useState("")
  const [cursorPosition, setCursorPosition] = useState(0)
  const inputRef = useRef<HTMLInputElement>(null)

  // Format phone number for display
  const formatPhoneNumber = (input: string): string => {
    // Remove all non-digit characters
    const digits = input.replace(/\D/g, "")
    
    // Handle different lengths
    if (digits.length === 0) return ""
    if (digits.length <= 3) return `(${digits}`
    if (digits.length <= 6) return `(${digits.slice(0, 3)}) ${digits.slice(3)}`
    if (digits.length <= 10) return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`
    
    // Handle 11 digits (with country code)
    if (digits.length === 11 && digits.startsWith("1")) {
      return `+1 (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7, 11)}`
    }
    
    // Truncate if too long
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 10)}`
  }

  // Extract clean phone number (digits only)
  const getCleanPhoneNumber = (input: string): string => {
    const digits = input.replace(/\D/g, "")
    
    // Remove leading 1 if it's an 11-digit number starting with 1
    if (digits.length === 11 && digits.startsWith("1")) {
      return digits.slice(1)
    }
    
    return digits
  }

  // Validate US phone number
  const isValidPhoneNumber = (phone: string): boolean => {
    const digits = phone.replace(/\D/g, "")
    
    // Valid if exactly 10 digits, or 11 digits starting with 1
    if (digits.length === 10) return true
    if (digits.length === 11 && digits.startsWith("1")) return true
    
    return false
  }

  // Calculate cursor position after formatting
  const calculateCursorPosition = (oldValue: string, newValue: string, oldCursor: number): number => {
    // Count digits before cursor in old value
    const digitsBeforeCursor = oldValue.slice(0, oldCursor).replace(/\D/g, "").length
    
    // Find position in new value that has the same number of digits before it
    let digitCount = 0
    for (let i = 0; i < newValue.length; i++) {
      if (/\d/.test(newValue[i])) {
        digitCount++
        if (digitCount === digitsBeforeCursor + 1) {
          return i + 1
        }
      }
    }
    
    return newValue.length
  }

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value
    const cursorPos = e.target.selectionStart || 0
    
    // Format the input
    const formatted = formatPhoneNumber(inputValue)
    const cleanPhone = getCleanPhoneNumber(inputValue)
    
    // Update display value
    setDisplayValue(formatted)
    
    // Calculate new cursor position
    const newCursorPos = calculateCursorPosition(displayValue, formatted, cursorPos)
    setCursorPosition(newCursorPos)
    
    // Call onChange with clean phone number
    onChange(cleanPhone)
  }

  // Handle key down for better UX
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow backspace to work naturally with formatting
    if (e.key === "Backspace") {
      const cursorPos = inputRef.current?.selectionStart || 0
      const char = displayValue[cursorPos - 1]
      
      // If deleting a formatting character, move cursor to delete the digit instead
      if (char && /[^\d]/.test(char)) {
        e.preventDefault()
        const newValue = displayValue.slice(0, cursorPos - 2) + displayValue.slice(cursorPos)
        const formatted = formatPhoneNumber(newValue)
        setDisplayValue(formatted)
        onChange(getCleanPhoneNumber(formatted))
        
        // Set cursor position after the deletion
        setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.setSelectionRange(cursorPos - 2, cursorPos - 2)
          }
        }, 0)
      }
    }
  }

  // Update display value when value prop changes
  useEffect(() => {
    if (value !== getCleanPhoneNumber(displayValue)) {
      const formatted = formatPhoneNumber(value)
      setDisplayValue(formatted)
    }
  }, [value])

  // Set cursor position after formatting
  useEffect(() => {
    if (inputRef.current && cursorPosition > 0) {
      inputRef.current.setSelectionRange(cursorPosition, cursorPosition)
    }
  }, [displayValue, cursorPosition])

  // Determine validation state
  const hasError = error || (displayValue && !isValidPhoneNumber(displayValue))
  const isValid = displayValue && isValidPhoneNumber(displayValue)

  return (
    <div className="space-y-2">
      {label && (
        <Label htmlFor={id} className="text-sm font-medium">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
      )}
      
      <div className="relative">
        <Input
          ref={inputRef}
          id={id}
          type="tel"
          value={displayValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={cn(
            "transition-colors",
            hasError && "border-red-500 focus:border-red-500 focus:ring-red-500",
            isValid && "border-green-500 focus:border-green-500 focus:ring-green-500",
            className
          )}
          autoComplete="tel"
        />
        
        {/* Visual indicator */}
        {displayValue && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {isValid ? (
              <div className="w-2 h-2 bg-green-500 rounded-full" />
            ) : hasError ? (
              <div className="w-2 h-2 bg-red-500 rounded-full" />
            ) : null}
          </div>
        )}
      </div>
      
      {/* Error message */}
      {hasError && (
        <p className="text-sm text-red-600">
          {error || "Please enter a valid US phone number (10 digits)"}
        </p>
      )}
      
      {/* Help text */}
      {!hasError && !isValid && (
        <p className="text-xs text-gray-500">
          Enter your phone number. Formats like (*************, ************, or 1234567890 are accepted.
        </p>
      )}
    </div>
  )
}
