"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { BusinessImageUpload } from "@/components/business-image-upload"

type BusinessInfoData = {
  name: string
  description: string
  yearEstablished: string
  website: string
  isEmergencyProvider: boolean
  imageUrl: string | null
}

interface BusinessRegistrationFormProps {
  data: BusinessInfoData
  updateData: (data: Partial<BusinessInfoData>) => void
  onNext: () => void
}

export function BusinessRegistrationForm({ data, updateData, onNext }: BusinessRegistrationFormProps) {
  const [errors, setErrors] = useState<Partial<Record<keyof BusinessInfoData, string>>>({})

  const validate = () => {
    const newErrors: Partial<Record<keyof BusinessInfoData, string>> = {}

    if (!data.name.trim()) {
      newErrors.name = "Business name is required"
    }

    if (!data.description.trim()) {
      newErrors.description = "Business description is required"
    } else if (data.description.length < 50) {
      newErrors.description = "Description must be at least 50 characters"
    }

    if (data.yearEstablished) {
      const year = Number.parseInt(data.yearEstablished)
      const currentYear = new Date().getFullYear()
      if (isNaN(year) || year < 1900 || year > currentYear) {
        newErrors.yearEstablished = `Year must be between 1900 and ${currentYear}`
      }
    }

    if (data.website && !/^(https?:\/\/)?(www\.)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(\/.*)?$/.test(data.website)) {
      newErrors.website = "Please enter a valid website URL"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validate()) {
      onNext()
    }
  }

  return (
    <div className="space-y-6 py-4">
      <div className="space-y-2">
        <Label htmlFor="business-name">
          Business Name <span className="text-red-500">*</span>
        </Label>
        <Input
          id="business-name"
          value={data.name}
          onChange={(e) => updateData({ name: e.target.value })}
          placeholder="Enter your business name"
        />
        {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
      </div>

      <div className="space-y-2">
        <Label htmlFor="business-description">
          Business Description <span className="text-red-500">*</span>
        </Label>
        <Textarea
          id="business-description"
          value={data.description}
          onChange={(e) => updateData({ description: e.target.value })}
          placeholder="Describe your business, services, and what makes you unique"
          rows={5}
        />
        {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
        <p className="text-xs text-gray-500">
          Minimum 50 characters. Include key information about your services, specialties, and experience.
        </p>
      </div>

      {/* Business Image Upload */}
      <BusinessImageUpload
        currentImageUrl={data.imageUrl}
        onImageChange={(imageUrl) => updateData({ imageUrl })}
        label="Business Image"
        description="Upload a photo of your business, logo, or storefront. This helps customers recognize and trust your business."
        required={false}
      />

      <div className="space-y-2">
        <Label htmlFor="year-established">Year Established</Label>
        <Input
          id="year-established"
          value={data.yearEstablished}
          onChange={(e) => updateData({ yearEstablished: e.target.value })}
          placeholder="e.g., 2010"
        />
        {errors.yearEstablished && <p className="text-sm text-red-500">{errors.yearEstablished}</p>}
      </div>

      <div className="space-y-2">
        <Label htmlFor="website">Website</Label>
        <Input
          id="website"
          value={data.website}
          onChange={(e) => updateData({ website: e.target.value })}
          placeholder="e.g., https://www.yourbusiness.com"
        />
        {errors.website && <p className="text-sm text-red-500">{errors.website}</p>}
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="emergency-provider"
          checked={data.isEmergencyProvider}
          onCheckedChange={(checked) => updateData({ isEmergencyProvider: checked })}
        />
        <Label htmlFor="emergency-provider">This business provides 24/7 emergency services</Label>
      </div>

      <div className="flex justify-end">
        <Button onClick={handleNext}>Next: Categories</Button>
      </div>
    </div>
  )
}
