import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth-config"
import userPrisma from "@/lib/user-prisma"
import { redirect } from "next/navigation"

export async function requireAdmin() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.email) {
    redirect("/login?redirect=/admin")
  }

  // Check if user has admin role
  const user = await userPrisma.user.findUnique({
    where: { email: session.user.email },
    select: { id: true, role: true, email: true, name: true }
  })

  if (!user || (user.role !== "ADMIN" && user.role !== "SUPER_ADMIN")) {
    redirect("/unauthorized")
  }

  return user
}

export async function requireSuperAdmin() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.email) {
    redirect("/login?redirect=/admin")
  }

  // Check if user has super admin role
  const user = await userPrisma.user.findUnique({
    where: { email: session.user.email },
    select: { id: true, role: true, email: true, name: true }
  })

  if (!user || user.role !== "SUPER_ADMIN") {
    redirect("/unauthorized")
  }

  return user
}

export async function isAdmin(email: string): Promise<boolean> {
  try {
    const user = await userPrisma.user.findUnique({
      where: { email },
      select: { role: true }
    })
    
    return user?.role === "ADMIN" || user?.role === "SUPER_ADMIN"
  } catch (error) {
    console.error("Error checking admin status:", error)
    return false
  }
}

export async function isSuperAdmin(email: string): Promise<boolean> {
  try {
    const user = await userPrisma.user.findUnique({
      where: { email },
      select: { role: true }
    })
    
    return user?.role === "SUPER_ADMIN"
  } catch (error) {
    console.error("Error checking super admin status:", error)
    return false
  }
}
