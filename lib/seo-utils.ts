import { Metadata } from 'next'

export const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://urgent-aid-locator.vercel.app'

export const defaultMetadata: Metadata = {
  title: {
    default: "Find 24/7 Emergency Services Near You | Urgent Aid Locator",
    template: "%s | Urgent Aid Locator"
  },
  description: "Search and discover nearby emergency plumbers, locksmiths, towing, urgent care clinics, veterinarians, and more. All available 24/7 when you need help most.",
  keywords: [
    "24/7 emergency services",
    "emergency plumber",
    "emergency locksmith", 
    "urgent care",
    "emergency towing",
    "emergency veterinarian",
    "24 hour services",
    "emergency repairs",
    "urgent services near me",
    "after hours services"
  ],
  authors: [{ name: "Urgent Aid Locator" }],
  creator: "Urgent Aid Locator",
  publisher: "Urgent Aid Locator",
  metadataBase: new URL(baseUrl),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'Find 24/7 Emergency Services Near You | Urgent Aid Locator',
    description: 'Search and discover nearby emergency plumbers, locksmiths, towing, urgent care clinics, veterinarians, and more. All available 24/7 when you need help most.',
    siteName: 'Urgent Aid Locator',
    images: [
      {
        url: '/opengraph-image.png',
        width: 1200,
        height: 630,
        alt: 'Urgent Aid Locator - Find 24/7 Emergency Services',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Find 24/7 Emergency Services Near You | Urgent Aid Locator',
    description: 'Search and discover nearby emergency plumbers, locksmiths, towing, urgent care clinics, veterinarians, and more. All available 24/7.',
    images: ['/opengraph-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export function generateCategoryMetadata(category: string): Metadata {
  const title = `${category} Services - 24/7 Emergency Help`
  const description = `Find reliable ${category.toLowerCase()} services available 24/7 near you. Emergency ${category.toLowerCase()} providers ready to help when you need it most.`
  
  return {
    title,
    description,
    keywords: [
      `emergency ${category.toLowerCase()}`,
      `24/7 ${category.toLowerCase()}`,
      `${category.toLowerCase()} near me`,
      `urgent ${category.toLowerCase()}`,
      `after hours ${category.toLowerCase()}`,
      "emergency services"
    ],
    openGraph: {
      title,
      description,
      type: "website",
      url: `/search?category=${encodeURIComponent(category)}`,
    },
  }
}

export function generateSearchMetadata(query?: string, location?: string, category?: string): Metadata {
  let title = "Search Emergency Services"
  let description = "Find 24/7 emergency services near you"
  
  if (category) {
    title = `${category} Services - 24/7 Emergency Help`
    description = `Find reliable ${category.toLowerCase()} services available 24/7 near you. Emergency ${category.toLowerCase()} providers ready to help when you need it most.`
  } else if (query && location) {
    title = `${query} in ${location} - 24/7 Emergency Services`
    description = `Find ${query.toLowerCase()} services in ${location}. Available 24/7 for emergency situations.`
  } else if (query) {
    title = `${query} - 24/7 Emergency Services Near You`
    description = `Find ${query.toLowerCase()} services available 24/7 near you. Emergency providers ready to help.`
  } else if (location) {
    title = `Emergency Services in ${location} - 24/7 Help Available`
    description = `Find emergency services in ${location}. Plumbers, locksmiths, towing, urgent care, and more available 24/7.`
  }
  
  const keywords = [
    "24/7 emergency services",
    "emergency services near me"
  ]
  
  if (query) keywords.push(`emergency ${query.toLowerCase()}`, `${query.toLowerCase()} near me`)
  if (location) keywords.push(`emergency services ${location}`)
  if (category) keywords.push(`emergency ${category.toLowerCase()}`, `${category.toLowerCase()} services`)
  
  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: "website",
    },
  }
}

export function generateBusinessSchema(business: any) {
  return {
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    name: business.name || business.businessName,
    description: business.description,
    url: business.website,
    telephone: business.phone,
    address: {
      '@type': 'PostalAddress',
      streetAddress: business.address,
      addressLocality: business.city,
      addressRegion: business.state || business.region,
      postalCode: business.zipCode,
      addressCountry: 'US'
    },
    geo: business.latitude && business.longitude ? {
      '@type': 'GeoCoordinates',
      latitude: business.latitude,
      longitude: business.longitude
    } : undefined,
    aggregateRating: business.rating ? {
      '@type': 'AggregateRating',
      ratingValue: business.rating,
      ratingCount: business.reviewCount || 1
    } : undefined,
    openingHours: business.hours || "Mo-Su 00:00-23:59", // Default to 24/7 for emergency services
    serviceArea: {
      '@type': 'GeoCircle',
      geoMidpoint: {
        '@type': 'GeoCoordinates',
        latitude: business.latitude,
        longitude: business.longitude
      },
      geoRadius: '50000' // 50km radius
    }
  }
}

export function generateBreadcrumbSchema(items: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `${baseUrl}${item.url}`
    }))
  }
}

export const emergencyServiceCategories = [
  'Emergency Plumber',
  'Emergency Locksmith', 
  '24 Hour Towing',
  'Urgent Care',
  'Emergency Veterinarian',
  'Emergency Electrician',
  'Emergency HVAC',
  'Emergency Dental',
  'Emergency Legal Services',
  'Emergency Auto Repair'
]

export const highValueKeywords = [
  'emergency plumber near me',
  'emergency locksmith near me',
  '24 hour towing near me',
  'urgent care near me',
  'emergency vet near me',
  'emergency electrician near me',
  'emergency hvac near me',
  'emergency dentist near me',
  'emergency services near me',
  '24/7 emergency services'
]
