import { PrismaClient } from '@/lib/generated/user-prisma';
import { Logger } from '@logtail/next';
import fs from 'fs';
import path from 'path';

// Global type for Next.js
declare global {
  var userPrisma: PrismaClient | undefined;
}

// Initialize the user database client
const userPrisma = global.userPrisma || new PrismaClient({
  datasources: {
    db: {
      url: process.env.USER_DATABASE_URL || 'file:./db/users.db',
    },
  },
  log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
});

// In development, preserve client across hot reloads
if (process.env.NODE_ENV !== 'production') {
  global.userPrisma = userPrisma;
}

// Handle database connection errors
const log = new Logger();
userPrisma.$on('error', (e) => {
  console.error('User database error:', e);
  log.error('User database error', {
    error: e.message,
    target: e.target,
    stack: e.stack
  });
  log.flush();
});

// Add connection check method
export async function checkUserDbConnection() {
  const log = new Logger();
  try {
    // First check if the file exists
    const fileCheck = await checkUserDbFileExists();
    log.info("Database file check before connection attempt", fileCheck);
    
    // Then try to connect
    const result = await userPrisma.$queryRaw`SELECT 1 as status`;
    log.info("User database connection successful", { 
      result,
      url: process.env.USER_DATABASE_URL
    });
    await log.flush();
    return true;
  } catch (error) {
    log.error("User database connection failed", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      url: process.env.USER_DATABASE_URL
    });
    await log.flush();
    return false;
  }
}

// Add a diagnostic function to check database file existence
export async function checkUserDbFileExists() {
  const log = new Logger();
  try {
    // Extract the file path from the DATABASE_URL
    const dbUrl = process.env.USER_DATABASE_URL || '';
    let dbPath = '';
    
    if (dbUrl.startsWith('file:')) {
      dbPath = dbUrl.substring(5); // Remove 'file:' prefix
    }
    
    // Handle relative paths
    if (dbPath.startsWith('../')) {
      dbPath = path.resolve(process.cwd(), dbPath);
    }
    
    log.info("Checking user database file", { 
      dbUrl,
      resolvedPath: dbPath
    });
    
    const exists = fs.existsSync(dbPath);
    log.info("User database file check", { 
      exists,
      path: dbPath
    });
    
    if (!exists) {
      log.error("User database file does not exist", { path: dbPath });
    }
    
    await log.flush();
    return { exists, path: dbPath };
  } catch (error) {
    log.error("Error checking user database file", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    await log.flush();
    return { exists: false, error: String(error) };
  }
}

export default userPrisma;
