import { Business } from "@/lib/data"
import prisma from "@/lib/prisma"
import userPrisma from "@/lib/user-prisma"

// Convert registered business to Business type for consistent interface
function convertRegisteredBusinessToBusiness(regBusiness: any): Business {
  return {
    id: `reg-${regBusiness.id}`, // Prefix to distinguish from main DB businesses
    name: regBusiness.businessName,
    categories: regBusiness.categories ? regBusiness.categories.split(",").map((c: string) => c.trim()) : [],
    phone: regBusiness.phone || "",
    address: regBusiness.address || "",
    city: regBusiness.city || "",
    searchTerm: regBusiness.categories ? regBusiness.categories.split(",")[0]?.trim() || "" : "",
    url: regBusiness.website || "",
    rating: 0, // Registered businesses start with no rating
    reviewCount: 0,
    isClosed: false,
    latitude: 0, // Would need geocoding service
    longitude: 0,
    zipCode: regBusiness.zipCode || undefined,
    region: regBusiness.state || undefined,
    price: undefined,
    imageUrl: regBusiness.imageUrl || undefined, // Include uploaded image
    is24Hours: regBusiness.is24Hours || false,
  }
}

// Enhanced search that queries both main database and registered businesses
export async function enhancedBusinessSearch({
  query,
  location,
  category,
  coordinates,
  radius = 10,
  limit = 25,
  offset = 0,
}: {
  query?: string
  location?: string
  category?: string
  coordinates?: { lat: number; lng: number }
  radius?: number
  limit?: number
  offset?: number
}) {
  try {
    // Search main database (scraped businesses)
    const mainBusinesses = await searchMainDatabase({
      query,
      location,
      category,
      coordinates,
      radius,
      limit: Math.ceil(limit * 0.8), // Reserve 20% for registered businesses
      offset,
    })

    // Search registered businesses
    const registeredBusinesses = await searchRegisteredBusinesses({
      query,
      location,
      category,
      limit: Math.ceil(limit * 0.2), // Up to 20% registered businesses
    })

    // Convert registered businesses to Business type
    const convertedRegisteredBusinesses = registeredBusinesses.map(convertRegisteredBusinessToBusiness)

    // Merge and sort results
    const allBusinesses = [...mainBusinesses, ...convertedRegisteredBusinesses]
    
    // Sort by relevance (registered businesses get slight boost for being local)
    const sortedBusinesses = allBusinesses.sort((a, b) => {
      // Registered businesses get a small boost
      const aIsRegistered = a.id.startsWith('reg-')
      const bIsRegistered = b.id.startsWith('reg-')
      
      if (aIsRegistered && !bIsRegistered) return -0.1
      if (!aIsRegistered && bIsRegistered) return 0.1
      
      // Otherwise sort by rating
      return (b.rating || 0) - (a.rating || 0)
    })

    return {
      businesses: sortedBusinesses.slice(0, limit),
      totalCount: mainBusinesses.length + registeredBusinesses.length,
    }
  } catch (error) {
    console.error('Enhanced search error:', error)
    return { businesses: [], totalCount: 0 }
  }
}

// Search main database (existing functionality)
async function searchMainDatabase({
  query,
  location,
  category,
  coordinates,
  radius,
  limit,
  offset,
}: {
  query?: string
  location?: string
  category?: string
  coordinates?: { lat: number; lng: number }
  radius?: number
  limit: number
  offset: number
}) {
  const whereConditions: any = {}
  const orConditions: any[] = []

  if (query) {
    orConditions.push(
      { term: { contains: query, mode: "insensitive" } },
      { name: { contains: query, mode: "insensitive" } },
      { categories: { contains: query, mode: "insensitive" } }
    )
  }

  if (category) {
    whereConditions.categories = { contains: category, mode: "insensitive" }
  }

  if (location) {
    orConditions.push(
      { city: { contains: location, mode: "insensitive" } },
      { region: { contains: location, mode: "insensitive" } },
      { address: { contains: location, mode: "insensitive" } }
    )
  }

  if (orConditions.length > 0) {
    whereConditions.OR = orConditions
  }

  const businesses = await prisma.business.findMany({
    where: whereConditions,
    take: limit,
    skip: offset,
  })

  return businesses.map((business) => ({
    id: business.id,
    name: business.name ?? "",
    categories: business.categories ? business.categories.split(",").map((c) => c.trim()) : [],
    phone: business.phone ?? "",
    address: business.address ?? "",
    city: business.city ?? "",
    searchTerm: (business as any).term ?? "",
    url: business.url ?? "",
    rating: business.rating ?? 0,
    reviewCount: business.reviewCount ?? 0,
    isClosed: Boolean(business.isClosed),
    latitude: business.latitude ?? 0,
    longitude: business.longitude ?? 0,
    zipCode: business.zipCode ?? undefined,
    region: business.region ?? undefined,
    price: business.price ?? undefined,
    imageUrl: business.imageUrl ?? undefined,
    is24Hours: (business as any).is24Hours ?? undefined,
  }))
}

// Search registered businesses
async function searchRegisteredBusinesses({
  query,
  location,
  category,
  limit,
}: {
  query?: string
  location?: string
  category?: string
  limit: number
}) {
  const whereConditions: any = {
    status: "APPROVED", // Only show approved businesses (exclude PENDING_REVIEW, REJECTED, NEEDS_CHANGES)
  }
  const orConditions: any[] = []

  if (query) {
    orConditions.push(
      { businessName: { contains: query, mode: "insensitive" } },
      { description: { contains: query, mode: "insensitive" } },
      { categories: { contains: query, mode: "insensitive" } }
    )
  }

  if (category) {
    whereConditions.categories = { contains: category, mode: "insensitive" }
  }

  if (location) {
    orConditions.push(
      { city: { contains: location, mode: "insensitive" } },
      { state: { contains: location, mode: "insensitive" } },
      { address: { contains: location, mode: "insensitive" } }
    )
  }

  if (orConditions.length > 0) {
    whereConditions.OR = orConditions
  }

  const registeredBusinesses = await userPrisma.registeredBusiness.findMany({
    where: whereConditions,
    take: limit,
  })

  return registeredBusinesses
}

// Get business by ID (handles both main and registered businesses)
export async function getBusinessByIdEnhanced(id: string): Promise<Business | null> {
  try {
    if (id.startsWith('reg-')) {
      // This is a registered business
      const registeredId = id.replace('reg-', '')
      const regBusiness = await userPrisma.registeredBusiness.findUnique({
        where: { id: registeredId },
      })
      
      if (!regBusiness) return null
      
      return convertRegisteredBusinessToBusiness(regBusiness)
    } else {
      // This is a main database business
      const business = await prisma.business.findUnique({
        where: { id },
      })

      if (!business) return null

      return {
        id: business.id,
        name: business.name ?? "",
        categories: business.categories ? business.categories.split(",").map((c) => c.trim()) : [],
        phone: business.phone ?? "",
        address: business.address ?? "",
        city: business.city ?? "",
        searchTerm: (business as any).term ?? "",
        url: business.url ?? "",
        rating: business.rating ?? 0,
        reviewCount: business.reviewCount ?? 0,
        isClosed: Boolean(business.isClosed),
        latitude: business.latitude ?? 0,
        longitude: business.longitude ?? 0,
        zipCode: business.zipCode ?? undefined,
        region: business.region ?? undefined,
        price: business.price ?? undefined,
        imageUrl: business.imageUrl ?? undefined,
        is24Hours: (business as any).is24Hours ?? undefined,
      }
    }
  } catch (error) {
    console.error('Error fetching business by ID:', error)
    return null
  }
}
