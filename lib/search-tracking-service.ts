import userPrisma from './user-prisma';
import { Business } from './data';
import prisma from './prisma'; // For getting business data
import { Logger } from '@logtail/next';

// Types for our search tracking
export type RecentSearch = {
  id: string;
  query: string;
  location: string;
  timestamp: Date;
};

export type PopularService = {
  searchTerm: string;
  searchCount: number;
  lastSearchedAt: Date;
  // We'll also include sample businesses that match this term
  sampleBusinesses?: Business[];
};

export type PopularBusiness = {
  businessId: string;
  businessName: string;
  viewCount: number;
  lastViewedAt: Date;
  business?: Business;
};

/**
 * Record a business view/click
 */
export async function recordBusinessView({
  userId,
  businessId,
  businessName,
}: {
  userId?: string;
  businessId: string;
  businessName: string;
}) {
  const log = new Logger();
  try {
    // Update global business view statistics
    await userPrisma.businessViewStats.upsert({
      where: {
        businessId,
      },
      update: {
        viewCount: {
          increment: 1,
        },
        lastViewedAt: new Date(),
      },
      create: {
        businessId,
        businessName,
        viewCount: 1,
        lastViewedAt: new Date(),
      },
    });

    console.log(`Recorded business view: "${businessName}" (${businessId}) for user: ${userId || 'anonymous'}`);
    log.info('Recorded business view', {
      businessId,
      businessName,
      userId: userId || 'anonymous'
    });
    await log.flush();
  } catch (error) {
    console.error('Error recording business view:', error);
    log.error('Error recording business view', {
      error: error instanceof Error ? error.message : String(error),
      businessId,
      businessName,
      userId: userId || 'anonymous'
    });
    await log.flush();
    throw error;
  }
}

/**
 * Record a search in both user history and global statistics
 */
export async function recordSearch({
  userId,
  searchQuery,
  searchLocation,
}: {
  userId?: string;
  searchQuery: string;
  searchLocation?: string;
}) {
  const log = new Logger();
  try {
    // Normalize the search query for statistics (lowercase, trim)
    const normalizedQuery = searchQuery.toLowerCase().trim();

    // Record in user's search history (if user is logged in)
    if (userId) {
      await userPrisma.searchHistory.create({
        data: {
          userId,
          searchQuery,
          searchLocation: searchLocation || null,
        },
      });
    } else {
      // Record anonymous search
      await userPrisma.searchHistory.create({
        data: {
          searchQuery,
          searchLocation: searchLocation || null,
        },
      });
    }

    // Update global search term statistics
    console.log(`Upserting search term stats for: "${normalizedQuery}"`);
    log.info('Upserting search term stats', {
      searchTerm: normalizedQuery,
      timestamp: new Date().toISOString(),
    });
    await userPrisma.searchTermStats.upsert({
      where: {
        searchTerm: normalizedQuery,
      },
      update: {
        searchCount: {
          increment: 1,
        },
        lastSearchedAt: new Date(),
      },
      create: {
        searchTerm: normalizedQuery,
        searchCount: 1,
        lastSearchedAt: new Date(),
      },
    });
    console.log(`Upserted search term stats for: "${normalizedQuery}"`);
    log.info('Upserted search term stats', {
      searchTerm: normalizedQuery,
      timestamp: new Date().toISOString(),
    });

    console.log(`Recorded search: "${searchQuery}" for user: ${userId || 'anonymous'}`);
    log.info('Recorded search', {
      searchQuery,
      searchLocation,
      userId: userId || 'anonymous'
    });
    await log.flush();
  } catch (error) {
    console.error('Error recording search:', error);
    log.error('Error recording search', {
      error: error instanceof Error ? error.message : String(error),
      searchQuery,
      userId: userId || 'anonymous'
    });
    await log.flush();
    // Don't throw - search tracking shouldn't break the main search functionality
  }
}

/**
 * Get recent searches for a user (or global recent searches if no userId provided)
 * Returns unique searches (no duplicates) ordered by most recent
 */
export async function getRecentSearches(
  userId?: string,
  limit: number = 10
): Promise<RecentSearch[]> {
  const log = new Logger();
  try {
    log.info("getRecentSearches - starting", { 
      userId, 
      limit,
      databaseUrl: process.env.USER_DATABASE_URL
    });
    
    // Check if userPrisma is initialized
    if (!userPrisma) {
      log.error("getRecentSearches - userPrisma is not initialized");
      throw new Error("Database client not initialized");
    }
    
    let whereCondition: any = {};
    if (userId) {
      whereCondition = { userId };
    }

    // Log the query we're about to execute
    log.info("getRecentSearches - executing query", { 
      whereCondition,
      orderBy: 'searchedAt: desc',
      take: limit * 3
    });

    // Get more searches than needed to account for duplicates
    const searches = await userPrisma.searchHistory.findMany({
      where: whereCondition,
      orderBy: {
        searchedAt: 'desc',
      },
      take: limit * 3, // Get more to account for duplicates
    });

    log.info("getRecentSearches - raw searches fetched", { 
      count: searches.length,
      firstSearchId: searches.length > 0 ? searches[0].id : 'none'
    });
    
    // Remove duplicates by keeping only the most recent search for each query+location combination
    const uniqueSearches = new Map<string, RecentSearch>();

    searches.forEach((search) => {
      const key = `${search.searchQuery}|${search.searchLocation || ''}`;
      if (!uniqueSearches.has(key)) {
        uniqueSearches.set(key, {
          id: search.id,
          query: search.searchQuery,
          location: search.searchLocation || '',
          timestamp: search.searchedAt,
        });
      }
    });

    const result = Array.from(uniqueSearches.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
    
    log.info("getRecentSearches - processed searches", { 
      uniqueCount: uniqueSearches.size,
      finalCount: result.length,
      firstResult: result.length > 0 ? result[0].query : 'none'
    });
    await log.flush();
    return result;
  } catch (error) {
    console.error('Error fetching recent searches:', error);
    log.error('getRecentSearches - error', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      databaseUrl: process.env.USER_DATABASE_URL
    });
    await log.flush();
    return [];
  }
}

/**
 * Get popular businesses based on view frequency
 */
export async function getPopularBusinesses(
  limit: number = 5
): Promise<PopularBusiness[]> {
  const log = new Logger();
  try {
    log.info("getPopularBusinesses - starting", {
      limit,
      databaseUrl: process.env.USER_DATABASE_URL
    });

    // Check if userPrisma is initialized
    if (!userPrisma) {
      log.error("getPopularBusinesses - userPrisma is not initialized");
      throw new Error("Database client not initialized");
    }

    // Get the most viewed businesses
    const popularBusinesses = await userPrisma.businessViewStats.findMany({
      orderBy: [
        { viewCount: 'desc' },
        { lastViewedAt: 'desc' },
      ],
      take: limit,
    });

    log.info("getPopularBusinesses - raw businesses fetched", {
      count: popularBusinesses.length,
      firstBusiness: popularBusinesses.length > 0 ?
        `${popularBusinesses[0].businessName} (${popularBusinesses[0].viewCount})` : 'none'
    });

    // Get full business details for each popular business
    const businessesWithDetails = await Promise.all(
      popularBusinesses.map(async (businessStat) => {
        try {
          // Get business details from main database
          const business = await prisma.business.findUnique({
            where: { id: businessStat.businessId },
          });

          const result: PopularBusiness = {
            businessId: businessStat.businessId,
            businessName: businessStat.businessName,
            viewCount: businessStat.viewCount,
            lastViewedAt: businessStat.lastViewedAt,
          };

          if (business) {
            result.business = {
              id: business.id,
              name: business.name || '',
              categories: business.categories ? business.categories.split(', ') : [],
              phone: business.phone || '',
              address: business.address || '',
              city: business.city || '',
              searchTerm: business.term || '',
              url: business.url || '',
              rating: business.rating || 0,
              reviewCount: business.reviewCount || 0,
              isClosed: Boolean(business.isClosed),
              latitude: business.latitude || 0,
              longitude: business.longitude || 0,
              zipCode: business.zipCode || '',
              state: business.state || '',
              imageUrl: business.imageUrl || '',
              price: business.price || '',
              distance: 0,
              is24Hours: business.term ?
                business.term.includes('24') ||
                business.term.includes('24/7') ||
                business.term.includes('after hours') : false,
            };
          }

          return result;
        } catch (error) {
          console.error(`Error fetching business details for ${businessStat.businessId}:`, error);
          return {
            businessId: businessStat.businessId,
            businessName: businessStat.businessName,
            viewCount: businessStat.viewCount,
            lastViewedAt: businessStat.lastViewedAt,
          };
        }
      })
    );

    log.info("getPopularBusinesses - completed", {
      finalCount: businessesWithDetails.length,
      firstBusiness: businessesWithDetails.length > 0 ?
        businessesWithDetails[0].businessName : 'none'
    });
    await log.flush();
    return businessesWithDetails;
  } catch (error) {
    console.error('Error fetching popular businesses:', error);
    log.error('getPopularBusinesses - error', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      databaseUrl: process.env.USER_DATABASE_URL
    });
    await log.flush();
    throw error;
  }
}

/**
 * Get popular emergency services based on search frequency
 */
export async function getPopularEmergencyServices(
  limit: number = 5
): Promise<PopularService[]> {
  const log = new Logger();
  try {
    log.info("getPopularEmergencyServices - starting", { 
      limit,
      databaseUrl: process.env.USER_DATABASE_URL
    });
    
    // Check if userPrisma is initialized
    if (!userPrisma) {
      log.error("getPopularEmergencyServices - userPrisma is not initialized");
      throw new Error("Database client not initialized");
    }
    
    // Log the query we're about to execute
    log.info("getPopularEmergencyServices - executing query", { 
      orderBy: ['searchCount: desc', 'lastSearchedAt: desc'],
      take: limit * 2
    });
    
    // Get the most searched terms, weighted by recency
    const popularTerms = await userPrisma.searchTermStats.findMany({
      orderBy: [
        { searchCount: 'desc' },
        { lastSearchedAt: 'desc' },
      ],
      take: limit * 2,
    });

    log.info("getPopularEmergencyServices - raw terms fetched", { 
      count: popularTerms.length,
      firstTerm: popularTerms.length > 0 ? 
        `${popularTerms[0].searchTerm} (${popularTerms[0].searchCount})` : 'none'
    });
    
    // Filter for emergency-related terms
    const emergencyKeywords = [
      '24', 'emergency', 'urgent', 'locksmith', 'towing', 'plumber', 'plumbers',
      'electrician', 'hvac', 'notary', 'repair', 'service', 'mobile',
      'after hours', 'immediate', 'fast', 'quick', 'hour', 'heating', 'cooling',
      'doctor', 'doctors', 'medical', 'clinic', 'hospital', 'dentist', 'dental',
      'pharmacy', 'urgent care', 'walk-in', 'veterinarian', 'vet', 'animal hospital'
    ];

    const emergencyTerms = popularTerms.filter((term) =>
      emergencyKeywords.some((keyword) =>
        term.searchTerm.toLowerCase().includes(keyword.toLowerCase())
      )
    );

    log.info("Emergency terms filtered", { 
      totalTerms: popularTerms.length, 
      emergencyTerms: emergencyTerms.length 
    });

    // If no emergency terms found, just return the most popular terms
    const finalTerms = emergencyTerms.length > 0 ? emergencyTerms.slice(0, limit) : popularTerms.slice(0, limit);

    console.log(`Popular terms found: ${popularTerms.length}, Emergency terms: ${emergencyTerms.length}, Final terms: ${finalTerms.length}`);
    console.log('Final terms:', finalTerms.map(t => `${t.searchTerm} (${t.searchCount})`));

    // For each popular term, get sample businesses
    const servicesWithBusinesses = await Promise.all(
      finalTerms.map(async (term) => {
        try {
          // Get sample businesses that match this search term
          const businesses = await prisma.business.findMany({
            where: {
              OR: [
                { term: { contains: term.searchTerm } },
                { name: { contains: term.searchTerm } },
                { categories: { contains: term.searchTerm } },
              ],
            },
            orderBy: [
              { rating: 'desc' },
              { reviewCount: 'desc' },
            ],
            take: 3, // Get top 3 businesses for this term
          });

          console.log(`Found ${businesses.length} businesses for term "${term.searchTerm}"`);

          const mappedBusinesses: Business[] = businesses.map((business) => ({
            id: business.id,
            name: business.name || '',
            categories: business.categories ? business.categories.split(', ') : [],
            phone: business.phone || '',
            address: business.address || '',
            city: business.city || '',
            searchTerm: business.term || '',
            url: business.url || '',
            rating: business.rating || 0,
            reviewCount: business.reviewCount || 0,
            isClosed: Boolean(business.isClosed),
            latitude: business.latitude || 0,
            longitude: business.longitude || 0,
            zipCode: business.zipCode || undefined,
            region: business.region || undefined,
            price: business.price || undefined,
            imageUrl: business.imageUrl || undefined,
            is24Hours: business.term ? 
              business.term.includes('24') || 
              business.term.includes('open 24') || 
              business.term.includes('24/7') || 
              business.term.includes('after hours') : false,
          }));

          return {
            searchTerm: term.searchTerm,
            searchCount: term.searchCount,
            lastSearchedAt: term.lastSearchedAt,
            sampleBusinesses: mappedBusinesses,
          };
        } catch (error) {
          console.error(`Error fetching businesses for term "${term.searchTerm}":`, error);
          return {
            searchTerm: term.searchTerm,
            searchCount: term.searchCount,
            lastSearchedAt: term.lastSearchedAt,
            sampleBusinesses: [],
          };
        }
      })
    );

    log.info("getPopularEmergencyServices - completed", { 
      finalCount: servicesWithBusinesses.length,
      firstService: servicesWithBusinesses.length > 0 ? 
        servicesWithBusinesses[0].searchTerm : 'none'
    });
    await log.flush();
    return servicesWithBusinesses;
  } catch (error) {
    console.error('Error fetching popular emergency services:', error);
    log.error('getPopularEmergencyServices - error', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      databaseUrl: process.env.USER_DATABASE_URL
    });
    await log.flush();
    return [];
  }
}

/**
 * Clear recent searches for a user
 */
export async function clearRecentSearches(userId: string): Promise<void> {
  const log = new Logger();
  try {
    await userPrisma.searchHistory.deleteMany({
      where: {
        userId,
      },
    });
    console.log(`Cleared recent searches for user: ${userId}`);
    log.info('Cleared recent searches', { userId });
    await log.flush();
  } catch (error) {
    console.error('Error clearing recent searches:', error);
    log.error('Error clearing recent searches', {
      error: error instanceof Error ? error.message : String(error),
      userId
    });
    await log.flush();
    throw error;
  }
}

/**
 * Remove a specific search from user's history
 */
export async function removeRecentSearch(userId: string, searchId: string): Promise<void> {
  const log = new Logger();
  try {
    await userPrisma.searchHistory.delete({
      where: {
        id: searchId,
        userId, // Ensure user can only delete their own searches
      },
    });
    console.log(`Removed search ${searchId} for user: ${userId}`);
    log.info('Removed recent search', { searchId, userId });
    await log.flush();
  } catch (error) {
    console.error('Error removing recent search:', error);
    log.error('Error removing recent search', {
      error: error instanceof Error ? error.message : String(error),
      searchId,
      userId
    });
    await log.flush();
    throw error;
  }
}

/**
 * Get search statistics (for admin/analytics purposes)
 */
export async function getSearchStatistics() {
  try {
    const totalSearches = await userPrisma.searchHistory.count();
    const uniqueUsers = await userPrisma.searchHistory.groupBy({
      by: ['userId'],
      where: {
        userId: {
          not: null,
        },
      },
    });
    const topTerms = await userPrisma.searchTermStats.findMany({
      orderBy: {
        searchCount: 'desc',
      },
      take: 10,
    });

    return {
      totalSearches,
      uniqueUsers: uniqueUsers.length,
      topTerms,
    };
  } catch (error) {
    console.error('Error fetching search statistics:', error);
    return {
      totalSearches: 0,
      uniqueUsers: 0,
      topTerms: [],
    };
  }
}
