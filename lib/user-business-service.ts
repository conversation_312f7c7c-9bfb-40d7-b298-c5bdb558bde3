import prisma from './prisma'; // Business database
import userPrisma from './user-prisma'; // User database
import { Business } from './data';
import bcrypt from 'bcryptjs';

// User management functions
export async function createUser(data: {
  email: string;
  password: string;
  name?: string;
}) {
  const passwordHash = await bcrypt.hash(data.password, 10);
  
  return userPrisma.user.create({
    data: {
      email: data.email,
      passwordHash,
      name: data.name,
    },
  });
}

export async function getUserByEmail(email: string) {
  return userPrisma.user.findUnique({
    where: { email },
  });
}

// Business saving functions
export async function getUserSavedBusinesses(userId: string): Promise<Business[]> {
  // First, get the saved business IDs from the user database
  const savedBusinesses = await userPrisma.savedBusiness.findMany({
    where: { userId },
    select: { businessId: true, notes: true }
  });
  
  if (savedBusinesses.length === 0) {
    return [];
  }
  
  // Then, get the full business details from the main database
  const businessIds = savedBusinesses.map(sb => sb.businessId);
  
  const businesses = await prisma.business.findMany({
    where: {
      id: { in: businessIds }
    }
  });
  
  // Map the businesses to include user notes
  return businesses.map(business => {
    const savedBusiness = savedBusinesses.find(sb => sb.businessId === business.id);
    
    return {
      id: business.id,
      name: business.name ?? "",
      categories: business.categories ? business.categories.split(",").map(c => c.trim()) : [],
      phone: business.phone ?? "",
      address: business.address ?? "",
      city: business.city ?? "",
      searchTerm: business.term ?? "",
      url: business.url ?? "",
      rating: business.rating ?? 0,
      reviewCount: business.reviewCount ?? 0,
      isClosed: Boolean(business.isClosed),
      latitude: business.latitude ?? 0,
      longitude: business.longitude ?? 0,
      zipCode: business.zipCode ?? undefined,
      region: business.region ?? undefined,
      price: business.price ?? undefined,
      imageUrl: business.imageUrl ?? undefined,
      is24Hours: business.term ? business.term.includes("24") : false,
      // Add user-specific data
      userNotes: savedBusiness?.notes
    } as Business & { userNotes?: string };
  });
}

// Save a business to a user's favorites
export async function saveBusinessForUser(userId: string, businessId: string, notes?: string) {
  // First check if the business exists in the main database
  const business = await prisma.business.findUnique({
    where: { id: businessId }
  });
  
  if (!business) {
    throw new Error(`Business with ID ${businessId} not found`);
  }
  
  // Then save the reference in the user database
  return userPrisma.savedBusiness.upsert({
    where: {
      userId_businessId: {
        userId,
        businessId
      }
    },
    update: {
      notes
    },
    create: {
      userId,
      businessId,
      notes
    }
  });
}

// Business registration functions
export async function createBusinessProfile(data: {
  userId: string;
  businessName: string;
  description?: string;
  phone: string;
  email: string;
  website?: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  categories: string;
  externalBusinessId?: string;
}) {
  return userPrisma.registeredBusiness.create({
    data,
  });
}

export async function getBusinessProfilesByUser(userId: string) {
  return userPrisma.registeredBusiness.findMany({
    where: { userId },
  });
}

// Subscription management
export async function createSubscription(data: {
  userId: string;
  planId: string;
  status: 'ACTIVE' | 'TRIALING';
  startDate: Date;
  currentPeriodEnd: Date;
}) {
  return userPrisma.subscription.create({
    data,
  });
}

export async function getActiveSubscription(userId: string) {
  return userPrisma.subscription.findFirst({
    where: {
      userId,
      status: 'ACTIVE',
      OR: [
        { endDate: null },
        { endDate: { gt: new Date() } }
      ]
    },
    include: {
      plan: true
    }
  });
}