// Data model based on the provided CSV schema
export type Business = {
  id: string
  name: string
  categories: string[]
  phone: string
  address: string
  city: string
  searchTerm: string
  url: string
  rating: number
  reviewCount: number
  isClosed: boolean
  latitude: number
  longitude: number
  zipCode?: string
  region?: string
  price?: string
  imageUrl?: string
  is24Hours?: boolean // Derived from searchTerm
}

export type SearchResultsData = {
  businesses: Business[];
  totalCount: number;
}

// Remove the entire hardcoded businesses array and refactor all functions to use Prisma queries

// Import Prisma client
import prisma from "./prisma"

// Helper functions to work with the business data

export async function getBusinessById(id: string): Promise<Business | null> {
  const business = await prisma.business.findUnique({
    where: { id },
  })

  if (!business) {
    return null
  }

  // Map Prisma business to Business type with null checks and defaults
  return {
    id: business.id,
    name: business.name ?? "",
    categories: business.categories ? business.categories.split(",").map((c) => c.trim()) : [],
    phone: business.phone ?? "",
    address: business.address ?? "",
    city: business.city ?? "",
    searchTerm: (business as any).term ?? "", // Adjusted field name from 'searchTerm' to 'term'
    url: business.url ?? "",
    rating: business.rating ?? 0,
    reviewCount: business.reviewCount ?? 0,
    isClosed: Boolean(business.isClosed),
    latitude: business.latitude ?? 0,
    longitude: business.longitude ?? 0,
    zipCode: business.zipCode ?? undefined,
    region: business.region ?? undefined,
    price: business.price ?? undefined,
    imageUrl: business.imageUrl ?? undefined,
    is24Hours: (business as any).is24Hours ?? undefined,
  }
}

// Adjusted to match Prisma schema: categories is string | null, so use contains without mode
export async function getBusinessesByCategory(category: string): Promise<Business[]> {
  const businesses = await prisma.business.findMany({
    where: {
      categories: {
        contains: category,
      },
    },
  })

  return businesses.map((business) => ({
    id: business.id,
    name: business.name ?? "",
    categories: business.categories ? business.categories.split(",").map((c) => c.trim()) : [],
    phone: business.phone ?? "",
    address: business.address ?? "",
    city: business.city ?? "",
    searchTerm: (business as any).term ?? "",
    url: business.url ?? "",
    rating: business.rating ?? 0,
    reviewCount: business.reviewCount ?? 0,
    isClosed: Boolean(business.isClosed),
    latitude: business.latitude ?? 0,
    longitude: business.longitude ?? 0,
    zipCode: business.zipCode ?? undefined,
    region: business.region ?? undefined,
    price: business.price ?? undefined,
    imageUrl: business.imageUrl ?? undefined,
    is24Hours: (business as any).is24Hours ?? undefined,
  }))
}

export async function getBusinessesBySearchTerm(term: string): Promise<Business[]> {
  const businesses = await prisma.business.findMany({
    where: {
      OR: [
        {
          term: {
            contains: term,
          },
        },
        {
          name: {
            contains: term,
          },
        },
        {
          categories: {
            contains: term,
          },
        },
      ],
    },
  })

  return businesses.map((business) => ({
    id: business.id,
    name: business.name ?? "",
    categories: Array.isArray(business.categories) ? business.categories : [],
    phone: business.phone ?? "",
    address: business.address ?? "",
    city: business.city ?? "",
    searchTerm: (business as any).term ?? "",
    url: business.url ?? "",
    rating: business.rating ?? 0,
    reviewCount: business.reviewCount ?? 0,
    isClosed: Boolean(business.isClosed),
    latitude: business.latitude ?? 0,
    longitude: business.longitude ?? 0,
    zipCode: business.zipCode ?? undefined,
    region: business.region ?? undefined,
    price: business.price ?? undefined,
    imageUrl: business.imageUrl ?? undefined,
    is24Hours: (business as any).is24Hours ?? undefined,
  }))
}

export async function getSimilarBusinesses(categoryList: string[], currentId: string): Promise<Business[]> {
  if (categoryList.length === 0) {
    return []
  }

  // Build OR conditions for each category to match any business with at least one matching category
  const orConditions = categoryList.map((category) => ({
    categories: {
      contains: category,
    },
  }))

  const businesses = await prisma.business.findMany({
    where: {
      id: {
        not: currentId,
      },
      OR: orConditions,
    },
  })

  return businesses.map((business) => ({
    id: business.id,
    name: business.name ?? "",
    categories: business.categories ? business.categories.split(",").map((c) => c.trim()) : [],
    phone: business.phone ?? "",
    address: business.address ?? "",
    city: business.city ?? "",
    searchTerm: (business as any).term ?? "",
    url: business.url ?? "",
    rating: business.rating ?? 0,
    reviewCount: business.reviewCount ?? 0,
    isClosed: Boolean(business.isClosed),
    latitude: business.latitude ?? 0,
    longitude: business.longitude ?? 0,
    zipCode: business.zipCode ?? undefined,
    region: business.region ?? undefined,
    price: business.price ?? undefined,
    imageUrl: business.imageUrl ?? undefined,
    is24Hours: (business as any).is24Hours ?? undefined,
  }))
}

export async function getAllCategories(): Promise<string[]> {
  const categories = await prisma.business.findMany({
    select: {
      categories: true,
    },
  })

  const categoriesSet = new Set<string>()
  categories.forEach((business) => {
    if (business.categories) {
      business.categories.split(",").forEach((category) => {
        categoriesSet.add(category.trim())
      })
    }
  })

  return Array.from(categoriesSet).sort()
}

export async function getAllSearchTerms(): Promise<string[]> {
  const searchTerms = await prisma.business.findMany({
    select: {
      searchTerm: true,
    },
  })

  const searchTermsSet = new Set<string>()
  searchTerms.forEach((business) => {
    if (business.searchTerm) {
      searchTermsSet.add(business.searchTerm)
    }
  })

  return Array.from(searchTermsSet).sort()
}

export async function getAllBusinesses(): Promise<Business[]> {
  const businesses = await prisma.business.findMany()

  return businesses.map((business) => ({
    id: business.id,
    name: business.name ?? "",
    categories: Array.isArray(business.categories) ? business.categories : [],
    phone: business.phone ?? "",
    address: business.address ?? "",
    city: business.city ?? "",
    searchTerm: (business as any).term ?? "",
    url: business.url ?? "",
    rating: business.rating ?? 0,
    reviewCount: business.reviewCount ?? 0,
    isClosed: Boolean(business.isClosed),
    latitude: business.latitude ?? 0,
    longitude: business.longitude ?? 0,
    zipCode: business.zipCode ?? undefined,
    region: business.region ?? undefined,
    price: business.price ?? undefined,
    imageUrl: business.imageUrl ?? undefined,
    is24Hours: (business as any).is24Hours ?? undefined,
  }))
}

export async function getBusinessesByLocation(location: string): Promise<Business[]> {
  const businesses = await prisma.business.findMany({
    where: {
      OR: [
        {
          city: {
            contains: location,
            mode: "insensitive",
          },
        },
        {
          region: {
            contains: location,
            mode: "insensitive",
          },
        },
        {
          address: {
            contains: location,
            mode: "insensitive",
          },
        },
      ],
    },
  })

  return businesses.map((business) => ({
    id: business.id,
    name: business.name ?? "",
    categories: Array.isArray(business.categories) ? business.categories : [],
    phone: business.phone ?? "",
    address: business.address ?? "",
    city: business.city ?? "",
    searchTerm: (business as any).term ?? "",
    url: business.url ?? "",
    rating: business.rating ?? 0,
    reviewCount: business.reviewCount ?? 0,
    isClosed: Boolean(business.isClosed),
    latitude: business.latitude ?? 0,
    longitude: business.longitude ?? 0,
    zipCode: business.zipCode ?? undefined,
    region: business.region ?? undefined,
    price: business.price ?? undefined,
    imageUrl: business.imageUrl ?? undefined,
    is24Hours: (business as any).is24Hours ?? undefined,
  }))
}
