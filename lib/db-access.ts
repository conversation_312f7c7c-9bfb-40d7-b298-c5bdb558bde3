import { prisma } from './prisma';
import { Business } from './data';

// Helper function to parse categories from the database
function parseCategories(categoriesStr: string | null): string[] {
  if (!categoriesStr) return [];
  return categoriesStr.split(', ');
}

// Helper function to determine if a business is 24 hours based on the term
function is24Hours(term: string | null): boolean {
  if (!term) return false;
  return term.includes('24') || term.includes('open 24') || term.includes('24/7') || term.includes('after hours');
}

// Helper function to convert Prisma Business to our Business type
function mapBusinessData(business: any): Business {
  return {
    id: business.id,
    name: business.name,
    categories: parseCategories(business.categories),
    phone: business.phone || '',
    address: business.address || '',
    city: business.city || '',
    searchTerm: business.term || '',
    url: business.url || '',
    rating: business.rating || 0,
    reviewCount: business.reviewCount || 0,
    isClosed: Boolean(business.isClosed),
    latitude: business.latitude || 0,
    longitude: business.longitude || 0,
    zipCode: business.zipCode || undefined,
    region: business.region || undefined,
    price: business.price || undefined,
    imageUrl: business.imageUrl || undefined,
    is24Hours: is24Hours(business.term),
  };
}

// Get a business by ID
export async function getBusinessById(id: string): Promise<Business | undefined> {
  const business = await prisma.business.findUnique({
    where: { id },
  });

  if (!business) {
    return undefined;
  }

  return mapBusinessData(business);
}

// Get all businesses
export async function getAllBusinesses(): Promise<Business[]> {
  const businesses = await prisma.business.findMany();
  return businesses.map(mapBusinessData);
}

// Get top businesses by rating and review count
export async function getTopBusinesses(limit: number = 3): Promise<Business[]> {
  const businesses = await prisma.business.findMany({
    orderBy: [
      { rating: 'desc' },
      { reviewCount: 'desc' }
    ],
    take: limit,
  });
  return businesses.map(mapBusinessData);
}

// Get businesses by search term
export async function getBusinessesBySearchTerm(query: string): Promise<Business[]> {
  if (!query) {
    return getAllBusinesses();
  }

  const businesses = await prisma.business.findMany({
    where: {
      OR: [
        { term: { contains: query } },
        { name: { contains: query } },
        { categories: { contains: query } }
      ],
    },
  });

  return businesses.map(mapBusinessData);
}

// Get businesses by location
export async function getBusinessesByLocation(location: string): Promise<Business[]> {
  if (!location) {
    return getAllBusinesses();
  }

  const businesses = await prisma.business.findMany({
    where: {
      OR: [
        { city: { contains: location } },
        { region: { contains: location } },
        { address: { contains: location } },
        { zipCode: { contains: location } },
      ],
    },
  });

  return businesses.map(mapBusinessData);
}

// Get businesses by category
export async function getBusinessesByCategory(category: string): Promise<Business[]> {
  if (!category) {
    return getAllBusinesses();
  }

  const businesses = await prisma.business.findMany({
    where: {
      categories: {
        contains: category,
      },
    },
  });

  return businesses.map(mapBusinessData);
}

// Get all unique search terms
export async function getAllSearchTerms(): Promise<string[]> {
  const terms = await prisma.business.findMany({
    select: {
      term: true,
    },
    distinct: ['term'],
    orderBy: {
      term: 'asc',
    },
  });

  return terms.map((t) => t.term || '').filter(Boolean);
}

// Get distinct categories using raw SQL with recursive CTE for SQLite
export async function getDistinctCategoriesRaw(): Promise<string[]> {
  try {
    const result = await prisma.$queryRaw<
      { category: string }[]
    >`
      WITH RECURSIVE split_categories(id, category, rest) AS (
        SELECT
          id,
          trim(substr(categories, 0, instr(categories || ',', ','))) AS category,
          substr(categories || ',', instr(categories || ',', ',') + 1) AS rest
        FROM businesses
        WHERE categories IS NOT NULL
        UNION ALL
        SELECT
          id,
          trim(substr(rest, 0, instr(rest, ','))) AS category,
          substr(rest, instr(rest, ',') + 1) AS rest
        FROM split_categories
        WHERE rest <> ''
      )
      SELECT DISTINCT category FROM split_categories WHERE category <> '' ORDER BY category ASC;
    `;

    const categories = result.map(row => row.category);
    console.log(`Fetched ${categories.length} distinct categories using raw SQL`);
    return categories;
  } catch (error) {
    console.error("Error fetching distinct categories with raw SQL:", error);
    return [];
  }
}

// Revert getAllCategories to previous JS-based method for immediate category availability
export async function getAllCategories(): Promise<string[]> {
  return getDistinctCategoriesRaw();
}

// Search businesses with combined filters
export async function searchBusinesses({
  query,
  location,
  category,
  distance,
  open24,
  verified,
  rating,
  limit,
  offset,
  sortBy,
}: {
  query?: string;
  location?: string;
  category?: string;
  distance?: number;
  open24?: boolean;
  verified?: boolean;
  rating?: number;
  limit?: number;
  offset?: number;
  sortBy?: "rating" | "distance";
}): Promise<{ businesses: Business[]; totalCount: number }> {
  // Build the where conditions based on provided filters
  const whereConditions: any = {
    AND: [],
  };

  // Add conditions only if filters are provided
  let hasFilters = false;

  if (query) {
    whereConditions.AND.push({
      OR: [
        { term: { contains: query } },
        { name: { contains: query } },
        { categories: { contains: query } }
      ],
    });
    hasFilters = true;
  }

  if (location) {
    whereConditions.AND.push({
      OR: [
        { city: { contains: location } },
        { region: { contains: location } },
        { address: { contains: location } },
        { zipCode: { contains: location } },
      ],
    });
    hasFilters = true;
  }

  if (category) {
    // Categories are stored as comma-separated strings like "Category1, Category2, Category3"
    // We need to match the exact category, which could be at the start, middle, or end of the string
    whereConditions.AND.push({
      OR: [
        { categories: { equals: category } },                    // Exact match for single category
        { categories: { startsWith: `${category}, ` } },         // At the start followed by comma
        { categories: { endsWith: `, ${category}` } },           // At the end with comma before
        { categories: { contains: `, ${category}, ` } },         // In the middle with commas on both sides
      ],
    });
    hasFilters = true;
    console.log(`Filtering by category: "${category}"`);
  }

  // Add filter for 24-hour businesses
  if (open24) {
    whereConditions.AND.push({
      OR: [
        { term: { contains: "24" } },
        { term: { contains: "open 24" } },
        { term: { contains: "24/7" } },
        { term: { contains: "after hours" } },
      ],
    });
    hasFilters = true;
  }

  // Add filter for verified businesses (using rating as a proxy for verification)
  if (verified) {
    whereConditions.AND.push({
      rating: {
        gte: 4.5,
      },
    });
    hasFilters = true;
  }

  // Add filter for minimum rating
  if (rating) {
    whereConditions.AND.push({
      rating: {
        gte: rating,
      },
    });
    hasFilters = true;
  }

  // Get total count based on filters
  const totalCount = hasFilters
    ? await prisma.business.count({ where: whereConditions })
    : await prisma.business.count();

  // Get paginated businesses based on filters, limit, and offset
  const businesses = hasFilters
    ? await prisma.business.findMany({
        where: whereConditions,
        take: limit,
        skip: offset,
        orderBy: sortBy === "rating" ? [{ rating: "desc" }, { rating: "asc" }] : undefined,
      })
    : await prisma.business.findMany({
        take: limit,
        skip: offset,
        orderBy: sortBy === "rating" ? [{ rating: "desc" }, { rating: "asc" }] : undefined,
      });

  // For non-coordinate searches, we can't filter by distance directly
  // But we can log that the distance parameter was provided
  if (distance) {
    console.log(`Distance filter set to ${distance} miles, but can only be applied with coordinates`);
  }

  return {
    businesses: businesses.map(mapBusinessData),
    totalCount,
  };
}
