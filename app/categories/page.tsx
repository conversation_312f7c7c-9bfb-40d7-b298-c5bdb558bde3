import Link from "next/link"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { FileText, Key, Truck, Zap, Thermometer, Stethoscope, ShieldAlert, Wrench, Fingerprint, ArrowRight } from "lucide-react"
import { getCategoriesAction } from "@/app/actions"

// Map category names to icons
const categoryIcons: Record<string, any> = {
  Notaries: FileText,
  "Translation Services": FileText,
  Fingerprinting: Fingerprint,
  Locksmiths: Key,
  "Security Services": ShieldAlert,
  Towing: Truck,
  "Roadside Assistance": Truck,
  "Auto Repair": Wrench,
  Electricians: Zap,
  Plumbers: Wrench,
  "Emergency Services": ShieldAlert,
  "Water Damage Restoration": Wrench,
  "Heating & Air Conditioning/HVAC": Thermometer,
  Pharmacies: Stethoscope,
  "Health & Medical": Stethoscope,
  "Legal Services": FileText,
}

export default async function CategoriesPage() {
  // Fetch categories from the database
  const categories = await getCategoriesAction()

  return (
    <main className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-blue-600 py-12 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">Emergency Service Categories</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Browse our comprehensive directory of emergency service categories to find the help you need, when you need it.
            </p>
          </div>
        </div>
      </div>

      {/* Categories Grid */}
      <div className="container mx-auto max-w-6xl px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => {
            const IconComponent = categoryIcons[category] || ShieldAlert

            return (
              <Card key={category} className="h-full hover:shadow-lg transition-all duration-200 group">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center text-center">
                    <div className="bg-blue-100 p-4 rounded-full mb-4 group-hover:bg-blue-200 transition-colors">
                      <IconComponent className="h-8 w-8 text-blue-600" />
                    </div>
                    <h2 className="text-xl font-bold mb-2 group-hover:text-blue-600 transition-colors">{category}</h2>
                    <p className="text-gray-600 mb-4 flex-grow">
                      Find reliable {category.toLowerCase()} services available 24/7 for emergencies
                    </p>
                    <Button asChild className="w-full group-hover:bg-blue-700 transition-colors">
                      <Link href={`/search?category=${encodeURIComponent(category)}`}>
                        Browse Services
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <Card className="bg-blue-50 border-blue-100">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">Can't find what you're looking for?</h2>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                Use our search feature to find specific services or browse all available emergency services in your area.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg">
                  <Link href="/search">
                    Search All Services
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg">
                  <Link href="/business/register">
                    List Your Business
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  )
}
