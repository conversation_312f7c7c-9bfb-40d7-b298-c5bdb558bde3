import { Suspense } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Eye, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react"
import { getPendingBusinesses, getBusinessReviewStats } from "@/app/admin-actions"
import { requireAdmin } from "@/lib/admin-auth"

async function BusinessStats() {
  const stats = await getBusinessReviewStats()
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-600">{stats.pending}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Approved</CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Rejected</CardTitle>
          <XCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Needs Changes</CardTitle>
          <AlertCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-yellow-600">{stats.needsChanges}</div>
        </CardContent>
      </Card>
    </div>
  )
}

async function PendingBusinessesList() {
  const businesses = await getPendingBusinesses()
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING_REVIEW":
        return <Badge variant="secondary" className="bg-orange-100 text-orange-800">Pending Review</Badge>
      case "APPROVED":
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Approved</Badge>
      case "REJECTED":
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Rejected</Badge>
      case "NEEDS_CHANGES":
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Needs Changes</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }
  
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Pending Business Reviews</CardTitle>
        <CardDescription>
          Businesses waiting for admin approval ({businesses.length} total)
        </CardDescription>
      </CardHeader>
      <CardContent>
        {businesses.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No businesses pending review</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Business Name</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {businesses.map((business) => (
                <TableRow key={business.id}>
                  <TableCell className="font-medium">
                    <div>
                      <div className="font-semibold">{business.businessName}</div>
                      {business.description && (
                        <div className="text-sm text-gray-500 truncate max-w-xs">
                          {business.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {business.categories.split(',')[0]?.trim()}
                      {business.categories.split(',').length > 1 && (
                        <span className="text-gray-500"> +{business.categories.split(',').length - 1}</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {business.city}, {business.state}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatDate(business.createdAt)}
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(business.status)}
                  </TableCell>
                  <TableCell>
                    <Link href={`/admin/review/${business.id}`}>
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        Review
                      </Button>
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}

export default async function AdminDashboard() {
  // Verify admin access
  await requireAdmin()
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <p className="text-gray-600 mt-2">Manage business registrations and reviews</p>
      </div>
      
      <Suspense fallback={<div>Loading statistics...</div>}>
        <BusinessStats />
      </Suspense>
      
      <Suspense fallback={<div>Loading pending businesses...</div>}>
        <PendingBusinessesList />
      </Suspense>
    </div>
  )
}
