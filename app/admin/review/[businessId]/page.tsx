import { use } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, MapPin, Phone, Globe, Clock, Calendar } from "lucide-react"
import { getBusinessForReview } from "@/app/admin-actions"
import { requireAdmin } from "@/lib/admin-auth"
import BusinessReviewActions from "./BusinessReviewActions"

export default function BusinessReviewPage({ params }: { params: Promise<{ businessId: string }> }) {
  const unwrappedParams = use(params)
  
  // This will run the auth check
  const adminPromise = requireAdmin()
  const businessPromise = getBusinessForReview(unwrappedParams.businessId)
  
  const admin = use(adminPromise)
  const business = use(businessPromise)
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING_REVIEW":
        return <Badge variant="secondary" className="bg-orange-100 text-orange-800">Pending Review</Badge>
      case "APPROVED":
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Approved</Badge>
      case "REJECTED":
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Rejected</Badge>
      case "NEEDS_CHANGES":
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Needs Changes</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }
  
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }
  
  // Parse hours if available
  let parsedHours = null
  try {
    if (business.hoursOfOperation) {
      parsedHours = JSON.parse(business.hoursOfOperation)
    }
  } catch (error) {
    console.error('Error parsing hours:', error)
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Link href="/admin/dashboard" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Link>
        
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{business.businessName}</h1>
            <p className="text-gray-600 mt-1">Business Review</p>
          </div>
          <div className="flex items-center gap-4">
            {getStatusBadge(business.status)}
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Business Information */}
          <Card>
            <CardHeader>
              <CardTitle>Business Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold text-gray-900">Description</h3>
                <p className="text-gray-700 mt-1">
                  {business.description || "No description provided"}
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-gray-900">Categories</h3>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {business.categories.split(',').map((category, index) => (
                      <Badge key={index} variant="outline">
                        {category.trim()}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                {business.yearEstablished && (
                  <div>
                    <h3 className="font-semibold text-gray-900">Year Established</h3>
                    <p className="text-gray-700 mt-1">{business.yearEstablished}</p>
                  </div>
                )}
              </div>
              
              <div>
                <h3 className="font-semibold text-gray-900">Emergency Services</h3>
                <p className="text-gray-700 mt-1">
                  {business.is24Hours ? "Available 24/7" : "Regular business hours"}
                </p>
              </div>
            </CardContent>
          </Card>
          
          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="font-medium">Phone</p>
                    <p className="text-gray-600">{business.phone}</p>
                  </div>
                </div>
                
                {business.email && (
                  <div className="flex items-center gap-3">
                    <Globe className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="font-medium">Email</p>
                      <p className="text-gray-600">{business.email}</p>
                    </div>
                  </div>
                )}
              </div>
              
              {business.website && (
                <div className="flex items-center gap-3">
                  <Globe className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="font-medium">Website</p>
                    <a href={business.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                      {business.website}
                    </a>
                  </div>
                </div>
              )}
              
              <div className="flex items-start gap-3">
                <MapPin className="h-5 w-5 text-gray-400 mt-1" />
                <div>
                  <p className="font-medium">Address</p>
                  <p className="text-gray-600">
                    {business.address}<br />
                    {business.city}, {business.state} {business.zipCode}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Business Hours */}
          {parsedHours && (
            <Card>
              <CardHeader>
                <CardTitle>Business Hours</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(parsedHours).map(([day, hours]: [string, any]) => (
                    <div key={day} className="flex justify-between py-2 border-b border-gray-100">
                      <span className="font-medium capitalize">{day}</span>
                      <span className="text-gray-600">
                        {hours.isClosed ? 'Closed' : `${hours.open} - ${hours.close}`}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
        
        {/* Sidebar */}
        <div className="space-y-6">
          {/* Business Image */}
          {business.imageUrl && (
            <Card>
              <CardHeader>
                <CardTitle>Business Image</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="aspect-video relative rounded-md overflow-hidden bg-gray-100">
                  <Image
                    src={business.imageUrl}
                    alt={business.businessName}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 400px"
                  />
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Review Actions */}
          <BusinessReviewActions businessId={business.id} currentStatus={business.status} />
          
          {/* Submission Details */}
          <Card>
            <CardHeader>
              <CardTitle>Submission Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <Calendar className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Submitted</p>
                  <p className="text-sm text-gray-600">{formatDate(business.createdAt)}</p>
                </div>
              </div>
              
              {business.reviewedAt && (
                <div className="flex items-center gap-3">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium">Last Reviewed</p>
                    <p className="text-sm text-gray-600">
                      {formatDate(business.reviewedAt)}
                    </p>
                  </div>
                </div>
              )}
              
              {business.adminNotes && (
                <div className="mt-4 p-3 bg-gray-50 rounded-md">
                  <p className="text-sm font-medium text-gray-900">Admin Notes</p>
                  <p className="text-sm text-gray-700 mt-1">{business.adminNotes}</p>
                </div>
              )}
              
              {business.rejectionReason && (
                <div className="mt-4 p-3 bg-red-50 rounded-md">
                  <p className="text-sm font-medium text-red-900">Rejection Reason</p>
                  <p className="text-sm text-red-700 mt-1">{business.rejectionReason}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
