"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CheckCircle, XCircle, AlertCircle, Loader2 } from "lucide-react"
import { approveBusiness, rejectBusiness, requestBusinessChanges } from "@/app/admin-actions"

interface BusinessReviewActionsProps {
  businessId: string
  currentStatus: string
}

const REJECTION_REASONS = [
  "Incomplete information",
  "Invalid business category",
  "Suspicious or fraudulent listing",
  "Duplicate business listing",
  "Inappropriate content",
  "Missing required documentation",
  "Business not operational",
  "Other"
]

export default function BusinessReviewActions({ businessId, currentStatus }: BusinessReviewActionsProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [action, setAction] = useState<string | null>(null)
  const [adminNotes, setAdminNotes] = useState("")
  const [rejectionReason, setRejectionReason] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const handleApprove = async () => {
    setIsLoading(true)
    setError(null)
    setAction("approve")
    
    try {
      const result = await approveBusiness(businessId, adminNotes || undefined)
      
      if (result.success) {
        setSuccess("Business approved successfully!")
        setTimeout(() => {
          router.push("/admin/dashboard")
        }, 2000)
      } else {
        setError(result.error || "Failed to approve business")
      }
    } catch (error) {
      setError("An error occurred while approving the business")
    } finally {
      setIsLoading(false)
      setAction(null)
    }
  }

  const handleReject = async () => {
    if (!rejectionReason) {
      setError("Please select a rejection reason")
      return
    }
    
    setIsLoading(true)
    setError(null)
    setAction("reject")
    
    try {
      const result = await rejectBusiness(businessId, rejectionReason, adminNotes || undefined)
      
      if (result.success) {
        setSuccess("Business rejected successfully!")
        setTimeout(() => {
          router.push("/admin/dashboard")
        }, 2000)
      } else {
        setError(result.error || "Failed to reject business")
      }
    } catch (error) {
      setError("An error occurred while rejecting the business")
    } finally {
      setIsLoading(false)
      setAction(null)
    }
  }

  const handleRequestChanges = async () => {
    if (!adminNotes.trim()) {
      setError("Please provide feedback for the requested changes")
      return
    }
    
    setIsLoading(true)
    setError(null)
    setAction("changes")
    
    try {
      const result = await requestBusinessChanges(businessId, adminNotes)
      
      if (result.success) {
        setSuccess("Change request sent successfully!")
        setTimeout(() => {
          router.push("/admin/dashboard")
        }, 2000)
      } else {
        setError(result.error || "Failed to request changes")
      }
    } catch (error) {
      setError("An error occurred while requesting changes")
    } finally {
      setIsLoading(false)
      setAction(null)
    }
  }

  const isActionDisabled = (actionType: string) => {
    return isLoading || (isLoading && action !== actionType)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Review Actions</CardTitle>
        <CardDescription>
          Take action on this business registration
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}
        
        {success && (
          <div className="bg-green-50 border border-green-200 rounded-md p-3">
            <p className="text-sm text-green-600">{success}</p>
          </div>
        )}
        
        {/* Admin Notes */}
        <div className="space-y-2">
          <Label htmlFor="admin-notes">Admin Notes (Optional)</Label>
          <Textarea
            id="admin-notes"
            placeholder="Add any notes or feedback for this business..."
            value={adminNotes}
            onChange={(e) => setAdminNotes(e.target.value)}
            rows={3}
          />
        </div>
        
        {/* Rejection Reason (only show when rejecting) */}
        <div className="space-y-2">
          <Label htmlFor="rejection-reason">Rejection Reason</Label>
          <Select value={rejectionReason} onValueChange={setRejectionReason}>
            <SelectTrigger>
              <SelectValue placeholder="Select a reason for rejection" />
            </SelectTrigger>
            <SelectContent>
              {REJECTION_REASONS.map((reason) => (
                <SelectItem key={reason} value={reason}>
                  {reason}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Action Buttons */}
        <div className="space-y-2">
          {currentStatus === "PENDING_REVIEW" && (
            <>
              <Button
                onClick={handleApprove}
                disabled={isActionDisabled("approve")}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                {isLoading && action === "approve" ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Approving...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Approve Business
                  </>
                )}
              </Button>
              
              <Button
                onClick={handleRequestChanges}
                disabled={isActionDisabled("changes")}
                variant="outline"
                className="w-full"
              >
                {isLoading && action === "changes" ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Requesting Changes...
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Request Changes
                  </>
                )}
              </Button>
              
              <Button
                onClick={handleReject}
                disabled={isActionDisabled("reject")}
                variant="destructive"
                className="w-full"
              >
                {isLoading && action === "reject" ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Rejecting...
                  </>
                ) : (
                  <>
                    <XCircle className="h-4 w-4 mr-2" />
                    Reject Business
                  </>
                )}
              </Button>
            </>
          )}
          
          {currentStatus === "APPROVED" && (
            <div className="text-center py-4">
              <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <p className="text-sm text-green-600 font-medium">Business Approved</p>
            </div>
          )}
          
          {currentStatus === "REJECTED" && (
            <div className="text-center py-4">
              <XCircle className="h-8 w-8 text-red-600 mx-auto mb-2" />
              <p className="text-sm text-red-600 font-medium">Business Rejected</p>
            </div>
          )}
          
          {currentStatus === "NEEDS_CHANGES" && (
            <div className="text-center py-4">
              <AlertCircle className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
              <p className="text-sm text-yellow-600 font-medium">Changes Requested</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
