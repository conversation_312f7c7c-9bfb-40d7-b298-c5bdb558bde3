import { redirect } from "next/navigation"
import { getCategoriesAction } from "@/app/actions"

export default async function CategoryPage({ params }: { params: { slug: string } }) {
  // Get all categories from the database
  const allCategories = await getCategoriesAction()

  // Convert slug to category name format (e.g., "plumbing" to "Plumbing")
  const categorySlug = decodeURIComponent(params.slug)

  // Find the matching category (case-insensitive)
  const category = allCategories.find((cat) => cat.toLowerCase() === categorySlug.toLowerCase())

  // If no matching category is found, redirect to categories page
  if (!category) {
    redirect("/categories")
  }

  // Redirect to search page with the category filter
  redirect(`/search?category=${encodeURIComponent(category)}`)
}
