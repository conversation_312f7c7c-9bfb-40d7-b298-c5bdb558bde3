import React from "react"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Metadata } from "next"
import Link from "next/link"

export const metadata: Metadata = {
  title: "How to Handle a Plumbing Emergency | Urgent Aid Locator",
  description:
    "Learn immediate steps to handle plumbing emergencies, minimize damage, and prepare for professional repairs with our expert guide.",
  keywords: [
    "plumbing emergency",
    "water leak",
    "pipe burst",
    "emergency plumbing",
    "plumber contact",
    "temporary plumbing fixes",
    "water damage prevention",
  ],
}

export default function PlumbingEmergencyGuide() {
  return (
    <main className="container mx-auto max-w-4xl px-4 py-12">
      <article>
        <h1 className="text-4xl font-bold mb-6">How to Handle a Plumbing Emergency</h1>
        <Badge className="mb-4">Plumbing</Badge>
        <Image
          src="https://images.pexels.com/photos/6419128/pexels-photo-6419128.jpeg"
          alt="Plumbing Emergency"
          width={800}
          height={400}
          className="rounded-lg mb-6 object-cover"
          priority
        />
        <p className="text-lg mb-6 text-gray-700">
          Plumbing emergencies can be overwhelming and cause serious damage if not handled promptly. I’ve experienced the stress firsthand and want to guide you through the essential steps to protect your home and prepare for professional help.
        </p>

        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold">Immediate Actions</h2>
          </CardHeader>
          <CardContent>
            <ol className="list-decimal list-inside space-y-3 text-gray-700">
              <li>
                <strong>Shut Off the Water Supply:</strong> Locate and turn off the main water valve to prevent further flooding.
              </li>
              <li>
                <strong>Drain Faucets and Toilets:</strong> Open faucets and flush toilets to drain remaining water from pipes.
              </li>
              <li>
                <strong>Contain the Leak:</strong> Use buckets, towels, or mops to contain and clean up water.
              </li>
              <li>
                <strong>Turn Off Electricity:</strong> If water is near electrical outlets or appliances, turn off the power to avoid hazards.
              </li>
              <li>
                <strong>Document the Damage:</strong> Take photos for insurance purposes.
              </li>
            </ol>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold">Temporary Fixes</h2>
          </CardHeader>
          <CardContent>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Use pipe clamps or rubber patches to temporarily seal leaks.</li>
              <li>Apply plumber’s tape to threaded pipe joints.</li>
              <li>Use epoxy putty for small cracks or holes.</li>
              <li>Replace damaged washers or seals if accessible.</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h2 className="text-2xl font-semibold">When to Call a Professional</h2>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">
              Contact a licensed plumber immediately if:
            </p>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>The leak is large or uncontrollable.</li>
              <li>Water damage is extensive.</li>
              <li>There is sewage backup or contamination.</li>
              <li>Temporary fixes do not hold.</li>
              <li>You are unsure about the source of the problem.</li>
            </ul>
          </CardContent>
        </Card>
      </article>
      <div className="mt-12 text-center">
        <Link
          href={{
            pathname: "/search",
            query: { query: "plumbing emergency water leak pipe burst" },
          }}
          className="inline-block rounded bg-blue-600 px-6 py-3 text-white font-semibold hover:bg-blue-700 transition"
          aria-label="Search for plumbing emergency services"
        >
          Search for Plumbing Emergency Services
        </Link>
      </div>
    </main>
  )
}
