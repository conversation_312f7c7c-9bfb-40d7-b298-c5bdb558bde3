import React from "react"
import Image from "next/image"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Metadata } from "next"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Complete Home Emergency Preparedness Checklist | Urgent Aid Locator",
  description:
    "Prepare your home for emergencies with our comprehensive checklist. Learn about essential supplies, emergency contacts, evacuation plans, and safety tips.",
  keywords: [
    "emergency preparedness",
    "home emergency checklist",
    "emergency supplies",
    "evacuation plan",
    "emergency contacts",
    "disaster readiness",
    "family safety",
  ],
}

export default function EmergencyPreparednessGuide() {
  return (
    <main className="container mx-auto max-w-4xl px-4 py-12">
      <article>
        <h1 className="text-4xl font-bold mb-6">Complete Home Emergency Preparedness Checklist</h1>
        <Badge className="mb-4">Emergency Preparedness</Badge>
        <Image
          src="https://images.pexels.com/photos/6519905/pexels-photo-6519905.jpeg"
          alt="Emergency Preparedness"
          width={800}
          height={400}
          className="rounded-lg mb-6 object-cover"
          priority
        />
        <p className="text-lg mb-6 text-gray-700">
          Preparing your home for emergencies can save lives and reduce damage. This comprehensive checklist covers essential supplies, contact information, and evacuation plans to keep your family safe.
        </p>

        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold">Essential Emergency Supplies</h2>
          </CardHeader>
          <CardContent>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Water: At least one gallon per person per day for at least three days</li>
              <li>Non-perishable food: Enough for at least three days</li>
              <li>Battery-powered or hand-crank radio</li>
              <li>Flashlight with extra batteries</li>
              <li>First aid kit</li>
              <li>Whistle to signal for help</li>
              <li>Dust mask to help filter contaminated air</li>
              <li>Moist towelettes, garbage bags, and plastic ties for sanitation</li>
              <li>Wrench or pliers to turn off utilities</li>
              <li>Manual can opener for food</li>
              <li>Local maps</li>
              <li>Cell phone with chargers and backup battery</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold">Emergency Contact Information</h2>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 mb-4">
              Keep a list of important phone numbers and addresses, including:
            </p>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Family members and close friends</li>
              <li>Doctors and medical facilities</li>
              <li>Local emergency services (fire, police, ambulance)</li>
              <li>Utility companies (gas, electric, water)</li>
              <li>Insurance agents</li>
              <li>Schools and workplaces</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h2 className="text-2xl font-semibold">Evacuation and Safety Plans</h2>
          </CardHeader>
          <CardContent>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Identify multiple evacuation routes from your home and neighborhood.</li>
              <li>Designate a meeting place for family members outside the home.</li>
              <li>Practice evacuation drills regularly.</li>
              <li>Know how to turn off utilities like gas, water, and electricity.</li>
              <li>Prepare a "go bag" with essential items for quick evacuation.</li>
              <li>Stay informed about local emergency alerts and warnings.</li>
            </ul>
          </CardContent>
        </Card>
      </article>
      <div className="mt-12 text-center">
        <Link
          href={{
            pathname: "/search",
            query: { query: "emergency preparedness home checklist" },
          }}
          className="inline-block rounded bg-blue-600 px-6 py-3 text-white font-semibold hover:bg-blue-700 transition"
          aria-label="Search for emergency preparedness resources"
        >
          Search for Emergency Preparedness Resources
        </Link>
      </div>
    </main>
  )
}
