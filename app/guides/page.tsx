import Link from "next/link"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Clock, FileText, Bookmark } from "lucide-react"

export default function GuidesPage() {
  // Sample emergency guides
  const emergencyGuides = [
    {
      id: "plumbing-emergency",
      title: "How to Handle a Plumbing Emergency",
      excerpt:
        "Learn the immediate steps to take during a plumbing emergency, from shutting off the water main to temporary fixes before professional help arrives.",
      category: "Plumbing",
      readTime: "5 min read",
      date: "May 2, 2023",
      image: "https://images.pexels.com/photos/6419128/pexels-photo-6419128.jpeg",
    },
    {
      id: "power-outage",
      title: "Power Outage Safety Guide",
      excerpt:
        "Essential safety tips during power outages, including food safety, heating alternatives, and when to call an emergency electrician.",
      category: "Electrical",
      readTime: "7 min read",
      date: "April 15, 2023",
      image: "https://images.pexels.com/photos/1114690/pexels-photo-1114690.jpeg",
    },
    {
      id: "lockout-solutions",
      title: "Home Lockout: Emergency Solutions",
      excerpt:
        "What to do when you're locked out of your home, including temporary access methods and how to find a reputable emergency locksmith.",
      category: "Locksmith",
      readTime: "4 min read",
      date: "June 10, 2023",
      image: "https://images.pexels.com/photos/792034/pexels-photo-792034.jpeg",
    },
    {
      id: "hvac-emergency",
      title: "HVAC Emergency Troubleshooting",
      excerpt:
        "Step-by-step guide to diagnosing common HVAC emergencies and when it's time to call a professional technician.",
      category: "HVAC",
      readTime: "6 min read",
      date: "March 22, 2023",
      image: "https://images.pexels.com/photos/32497161/pexels-photo-32497161.jpeg",
    },
    {
      id: "car-breakdown",
      title: "Roadside Emergency: Car Breakdown Guide",
      excerpt:
        "What to do when your car breaks down, from safely pulling over to contacting roadside assistance services.",
      category: "Automotive",
      readTime: "8 min read",
      date: "May 28, 2023",
      image: "https://images.pexels.com/photos/6078/road-man-broken-car-6078.jpg",
    },
    {
      id: "water-damage",
      title: "Responding to Water Damage",
      excerpt:
        "Immediate steps to take after flooding or water damage to minimize property damage before restoration services arrive.",
      category: "Water Damage",
      readTime: "5 min read",
      date: "April 3, 2023",
      image: "https://images.pexels.com/photos/8624465/pexels-photo-8624465.jpeg",
    },
  ]

  // Featured guide
  const featuredGuide = {
    id: "emergency-preparedness",
    title: "Complete Home Emergency Preparedness Checklist",
    excerpt:
      "A comprehensive guide to preparing your home for any emergency, including essential supplies, contact information, and evacuation plans.",
    category: "Emergency Preparedness",
    readTime: "10 min read",
    date: "June 1, 2023",
    image: "https://images.pexels.com/photos/6519905/pexels-photo-6519905.jpeg",
  }

  return (
    <main className="container mx-auto max-w-6xl px-4 py-12">
      <div className="mb-8 text-center">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">Emergency Guides</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Expert advice and step-by-step instructions to help you handle emergencies until professional help arrives.
        </p>
      </div>

      {/* Featured Guide */}
      <div className="mb-12">
        <Card className="overflow-hidden">
          <div className="md:flex">
            <div className="md:w-1/2 relative h-64 md:h-auto">
              <Image
                src={featuredGuide.image || "/placeholder.svg"}
                alt={featuredGuide.title}
                fill
                className="object-cover"
              />
            </div>
            <div className="md:w-1/2 p-6 md:p-8 flex flex-col justify-between">
              <div>
                <Badge className="mb-2">{featuredGuide.category}</Badge>
                <h2 className="text-2xl md:text-3xl font-bold mb-4">{featuredGuide.title}</h2>
                <p className="text-gray-600 mb-4">{featuredGuide.excerpt}</p>
                <div className="flex items-center text-sm text-gray-500 mb-6">
                  <Clock className="h-4 w-4 mr-1" />
                  <span className="mr-4">{featuredGuide.readTime}</span>
                  <span>{featuredGuide.date}</span>
                </div>
              </div>
              <div className="flex gap-3">
                <Button asChild>
                  <Link href={`/guides/${featuredGuide.id}`}>Read Guide</Link>
                </Button>
                <Button variant="outline">
                  <Bookmark className="h-4 w-4 mr-2" />
                  Save for Later
                </Button>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* All Guides */}
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">All Emergency Guides</h2>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              Most Recent
            </Button>
            <Button variant="outline" size="sm">
              Most Popular
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {emergencyGuides.map((guide) => (
            <Card key={guide.id} className="overflow-hidden flex flex-col h-full">
              <div className="relative h-48">
                <Image src={guide.image || "/placeholder.svg"} alt={guide.title} fill className="object-cover" />
              </div>
              <CardContent className="p-6 flex-grow">
                <Badge className="mb-2">{guide.category}</Badge>
                <h3 className="text-xl font-bold mb-2">{guide.title}</h3>
                <p className="text-gray-600 mb-4">{guide.excerpt}</p>
                <div className="flex items-center text-sm text-gray-500">
                  <Clock className="h-4 w-4 mr-1" />
                  <span className="mr-4">{guide.readTime}</span>
                  <span>{guide.date}</span>
                </div>
              </CardContent>
              <CardFooter className="px-6 pb-6 pt-0">
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/guides/${guide.id}`}>
                    <FileText className="h-4 w-4 mr-2" />
                    Read Guide
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        <div className="mt-8 text-center">
          <Button variant="outline" size="lg">
            Load More Guides
          </Button>
        </div>
      </div>
    </main>
  )
}
