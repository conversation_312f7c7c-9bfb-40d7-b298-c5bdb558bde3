import React from "react"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Metadata } from "next"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Home Lockout Emergency Solutions | Urgent Aid Locator",
  description:
    "Locked out of your home? Discover practical emergency solutions and tips for finding a reputable locksmith. Stay safe and regain access quickly.",
  keywords: [
    "home lockout",
    "emergency locksmith",
    "lockout solutions",
    "locksmith tips",
    "safe lockout",
    "emergency access",
    "lock replacement",
  ],
}

export default function LockoutSolutionsGuide() {
  return (
    <main className="container mx-auto max-w-4xl px-4 py-12">
      <article>
        <h1 className="text-4xl font-bold mb-6">Home Lockout: Emergency Solutions</h1>
        <Badge className="mb-4">Locksmith</Badge>
        <Image
          src="https://images.pexels.com/photos/792034/pexels-photo-792034.jpeg"
          alt="Home Lockout"
          width={800}
          height={400}
          className="rounded-lg mb-6 object-cover"
          priority
        />
        <p className="text-lg mb-6 text-gray-700">
          Getting locked out of your home can be a frustrating and stressful experience. I’ve been there, and I know how important it is to stay calm and take the right steps to regain access safely. This guide offers practical advice and tips on finding a trustworthy emergency locksmith.
        </p>

        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold">Immediate Actions to Take</h2>
          </CardHeader>
          <CardContent>
            <ol className="list-decimal list-inside space-y-3 text-gray-700">
              <li>
                <strong>Stay Calm and Assess:</strong> Check all possible entry points such as windows or back doors that might be unlocked.
              </li>
              <li>
                <strong>Contact Household Members:</strong> If others have keys, reach out to them for assistance.
              </li>
              <li>
                <strong>Use Spare Keys:</strong> If you have given a spare key to a trusted neighbor or friend, contact them.
              </li>
              <li>
                <strong>Avoid Forced Entry:</strong> Do not attempt to break windows or doors as this can cause damage and injury.
              </li>
              <li>
                <strong>Call a Professional Locksmith:</strong> If no other options are available, contact a licensed emergency locksmith.
              </li>
            </ol>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold">Choosing a Reputable Locksmith</h2>
          </CardHeader>
          <CardContent>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Verify licensing and insurance.</li>
              <li>Check online reviews and ratings.</li>
              <li>Ask for an upfront estimate before work begins.</li>
              <li>Confirm 24/7 emergency availability.</li>
              <li>Ensure they use non-destructive entry methods.</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h2 className="text-2xl font-semibold">Preventive Tips</h2>
          </CardHeader>
          <CardContent>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Keep spare keys in secure locations.</li>
              <li>Consider installing smart locks with remote access.</li>
              <li>Regularly update locks and security systems.</li>
              <li>Inform trusted neighbors about your spare key arrangements.</li>
            </ul>
          </CardContent>
        </Card>
      </article>
      <div className="mt-12 text-center">
        <Link
          href={{
            pathname: "/search",
            query: { query: "home lockout emergency locksmith" },
          }}
          className="inline-block rounded bg-blue-600 px-6 py-3 text-white font-semibold hover:bg-blue-700 transition"
          aria-label="Search for home lockout locksmith services"
        >
          Search for Home Lockout Locksmith Services
        </Link>
      </div>
    </main>
  )
}
