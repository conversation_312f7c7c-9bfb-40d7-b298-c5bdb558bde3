import React from "react"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Metadata } from "next"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Responding to Water Damage | Urgent Aid Locator",
  description:
    "Learn immediate steps to respond to water damage, prevent mold growth, and know when to call restoration professionals with our expert guide.",
  keywords: [
    "water damage",
    "flood response",
    "mold prevention",
    "water damage restoration",
    "property damage",
    "insurance claims",
    "water leak",
  ],
}

export default function WaterDamageGuide() {
  return (
    <main className="container mx-auto max-w-4xl px-4 py-12">
      <article>
        <h1 className="text-4xl font-bold mb-6">Responding to Water Damage</h1>
        <Badge className="mb-4">Water Damage</Badge>
        <Image
          src="https://images.pexels.com/photos/8624465/pexels-photo-8624465.jpeg"
          alt="Water Damage"
          width={800}
          height={400}
          className="rounded-lg mb-6 object-cover"
          priority
        />
        <p className="text-lg mb-6 text-gray-700">
          Water damage from flooding or leaks can cause serious property damage and health risks. I’ve been through similar situations and want to help you take the right steps to minimize damage and prepare for restoration.
        </p>

        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold">Immediate Steps to Take</h2>
          </CardHeader>
          <CardContent>
            <ol className="list-decimal list-inside space-y-3 text-gray-700">
              <li>
                <strong>Ensure Safety First:</strong> Turn off electricity and gas supply to affected areas to prevent hazards.
              </li>
              <li>
                <strong>Stop the Water Source:</strong> Identify and stop the source of water if possible.
              </li>
              <li>
                <strong>Remove Excess Water:</strong> Use pumps, wet vacuums, or mops to remove standing water.
              </li>
              <li>
                <strong>Ventilate and Dry:</strong> Open windows and use fans and dehumidifiers to dry the area.
              </li>
              <li>
                <strong>Document Damage:</strong> Take photos and notes for insurance claims.
              </li>
            </ol>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold">Preventing Mold and Further Damage</h2>
          </CardHeader>
          <CardContent>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Remove wet carpets, furniture, and drywall if soaked.</li>
              <li>Clean and disinfect affected areas to prevent mold growth.</li>
              <li>Use mold inhibitors on surfaces after cleaning.</li>
              <li>Keep humidity levels low with dehumidifiers.</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h2 className="text-2xl font-semibold">When to Call Restoration Professionals</h2>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">
              Contact water damage restoration experts if:
            </p>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Water damage is extensive or from contaminated sources.</li>
              <li>Structural damage is suspected.</li>
              <li>Mold growth is visible or suspected.</li>
              <li>Damage affects electrical systems or appliances.</li>
              <li>You need assistance with insurance claims and repairs.</li>
            </ul>
          </CardContent>
        </Card>
      </article>
      <div className="mt-12 text-center">
        <Link
          href={{
            pathname: "/search",
            query: { query: "water damage flood response mold prevention" },
          }}
          className="inline-block rounded bg-blue-600 px-6 py-3 text-white font-semibold hover:bg-blue-700 transition"
          aria-label="Search for water damage restoration services"
        >
          Search for Water Damage Restoration Services
        </Link>
      </div>
    </main>
  )
}
