import React from "react"
import Image from "next/image"
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Metadata } from "next"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Power Outage Safety Guide | Urgent Aid Locator",
  description:
    "Stay safe during power outages with our essential safety tips. Learn about food safety, heating alternatives, and when to call an emergency electrician.",
  keywords: [
    "power outage",
    "safety tips",
    "emergency electrician",
    "food safety",
    "heating alternatives",
    "generator safety",
    "power outage preparedness",
  ],
}

export default function PowerOutageGuide() {
  return (
    <main className="container mx-auto max-w-4xl px-4 py-12">
      <article>
        <h1 className="text-4xl font-bold mb-6">Power Outage Safety Guide</h1>
        <Badge className="mb-4">Electrical</Badge>
        <Image
          src="https://images.pexels.com/photos/1114690/pexels-photo-1114690.jpeg"
          alt="Power Outage"
          width={800}
          height={400}
          className="rounded-lg mb-6 object-cover"
          priority
        />
        <p className="text-lg mb-6 text-gray-700">
          Power outages can disrupt daily life and pose safety risks. I’ve experienced the challenges firsthand and want to share essential safety tips to help you manage during an outage effectively.
        </p>

        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold">Safety Tips During Power Outages</h2>
          </CardHeader>
          <CardContent>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Use flashlights instead of candles to reduce fire risk.</li>
              <li>Keep refrigerator and freezer doors closed to preserve food.</li>
              <li>Turn off and unplug electrical appliances to avoid damage from power surges.</li>
              <li>Use generators outdoors and away from windows to prevent carbon monoxide poisoning.</li>
              <li>Keep a battery-powered radio to stay informed about updates.</li>
              <li>Check on neighbors, especially the elderly or disabled.</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold">Heating Alternatives</h2>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 mb-4">
              During cold weather outages, consider these safe heating options:
            </p>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Use battery-powered or propane heaters designed for indoor use.</li>
              <li>Wear layered clothing and use blankets.</li>
              <li>Avoid using outdoor grills or camp stoves indoors.</li>
              <li>Ensure proper ventilation to prevent carbon monoxide buildup.</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h2 className="text-2xl font-semibold">When to Call an Emergency Electrician</h2>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">
              Contact a licensed electrician if you experience:
            </p>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Repeated power outages or flickering lights.</li>
              <li>Burning smells or sparks from outlets or switches.</li>
              <li>Electrical shocks when touching appliances.</li>
              <li>Tripped breakers that won’t reset.</li>
            </ul>
          </CardContent>
        </Card>
      </article>
      <div className="mt-12 text-center">
        <Link
          href={{
            pathname: "/search",
            query: { query: "power outage safety emergency electrician" },
          }}
          className="inline-block rounded bg-blue-600 px-6 py-3 text-white font-semibold hover:bg-blue-700 transition"
          aria-label="Search for power outage emergency services"
        >
          Search for Power Outage Emergency Services
        </Link>
      </div>
    </main>
  )
}
