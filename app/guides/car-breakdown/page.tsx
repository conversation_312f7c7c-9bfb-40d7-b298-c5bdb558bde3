import React from "react"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Metadata } from "next"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Roadside Emergency: Car Breakdown Guide | Urgent Aid Locator",
  description:
    "Stay safe during a car breakdown with our expert roadside emergency guide. Learn immediate steps, safety tips, and how to prepare your vehicle for emergencies.",
  keywords: [
    "car breakdown",
    "roadside emergency",
    "vehicle safety",
    "emergency preparedness",
    "roadside assistance",
    "car trouble",
    "vehicle emergency tips",
  ],
}

export default function CarBreakdownGuide() {
  return (
    <main className="container mx-auto max-w-4xl px-4 py-12">
      <article>
        <h1 className="text-4xl font-bold mb-6">Roadside Emergency: Car Breakdown Guide</h1>
        <Badge className="mb-4">Automotive</Badge>
        <Image
          src="https://images.pexels.com/photos/6078/road-man-broken-car-6078.jpg"
          alt="Car Breakdown"
          width={800}
          height={400}
          className="rounded-lg mb-6 object-cover"
          priority
        />
        <p className="text-lg mb-6 text-gray-700">
          Facing a car breakdown unexpectedly can be overwhelming and stressful. As someone who has been through it, I understand how important it is to stay calm and take the right steps to ensure your safety and get help quickly. This guide will walk you through practical, easy-to-follow advice to handle roadside emergencies with confidence.
        </p>

        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold">Immediate Steps to Take</h2>
          </CardHeader>
          <CardContent>
            <ol className="list-decimal list-inside space-y-3 text-gray-700">
              <li>
                <strong>Stay Calm and Assess Safety:</strong> The first thing to do is turn on your hazard lights to alert other drivers. If you can, carefully steer your vehicle to the shoulder or a safe spot away from traffic. Your safety is the top priority.
              </li>
              <li>
                <strong>Exit the Vehicle Safely:</strong> If it’s safe, get out of the car on the side away from traffic. Avoid standing near the road where passing vehicles could pose a danger.
              </li>
              <li>
                <strong>Call for Help:</strong> Use your phone to contact roadside assistance, a tow service, or emergency services if needed. Don’t hesitate to ask for help.
              </li>
              <li>
                <strong>Use Emergency Supplies:</strong> If you have emergency cones, flares, or reflective triangles, place them behind your vehicle to increase visibility and warn other drivers.
              </li>
              <li>
                <strong>Stay Visible and Safe:</strong> If you have a reflective vest, wear it. If the area feels unsafe, it’s best to stay inside your vehicle until help arrives.
              </li>
            </ol>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold">Preparing for Roadside Emergencies</h2>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 mb-4">
              Preparation is key to handling emergencies smoothly. Here are some essentials to keep in your vehicle at all times:
            </p>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>A well-stocked first aid kit to handle minor injuries.</li>
              <li>A reliable flashlight with extra batteries for visibility at night.</li>
              <li>A basic tool kit for minor repairs.</li>
              <li>Jumper cables to help with battery issues.</li>
              <li>Reflective warning triangles or flares to alert other drivers.</li>
              <li>Water and non-perishable snacks to keep you hydrated and energized.</li>
              <li>A phone charger and a list of emergency contact numbers.</li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h2 className="text-2xl font-semibold">Additional Tips</h2>
          </CardHeader>
          <CardContent>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Keep up with regular vehicle maintenance to reduce the risk of breakdowns.</li>
              <li>Always inform someone about your travel plans and expected arrival time.</li>
              <li>Learn how to change a tire and check your vehicle’s fluids for added confidence.</li>
              <li>Stay alert to your surroundings and trust your instincts when in unfamiliar areas.</li>
            </ul>
          </CardContent>
        </Card>
      </article>
      <div className="mt-12 text-center">
        <Link
          href={{
            pathname: "/search",
            query: { query: "car breakdown roadside emergency" },
          }}
          className="inline-block rounded bg-blue-600 px-6 py-3 text-white font-semibold hover:bg-blue-700 transition"
          aria-label="Search for car breakdown services"
        >
          Search for Car Breakdown Services
        </Link>
      </div>
    </main>
  )
}
