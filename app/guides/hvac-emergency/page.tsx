import React from "react"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Metadata } from "next"
import Link from "next/link"

export const metadata: Metadata = {
  title: "HVAC Emergency Troubleshooting Guide | Urgent Aid Locator",
  description:
    "Learn how to troubleshoot common HVAC emergencies with our expert guide. Discover when to call a professional technician and how to maintain your HVAC system.",
  keywords: [
    "HVAC emergency",
    "HVAC troubleshooting",
    "heating and cooling issues",
    "HVAC maintenance",
    "emergency HVAC repair",
    "HVAC technician",
    "HVAC safety tips",
  ],
}

export default function HvacEmergencyGuide() {
  return (
    <main className="container mx-auto max-w-4xl px-4 py-12">
      <article>
        <h1 className="text-4xl font-bold mb-6">HVAC Emergency Troubleshooting</h1>
        <Badge className="mb-4">HVAC</Badge>
        <Image
          src="https://images.pexels.com/photos/32497161/pexels-photo-32497161.jpeg"
          alt="HVAC Emergency"
          width={800}
          height={400}
          className="rounded-lg mb-6 object-cover"
          priority
        />
        <p className="text-lg mb-6 text-gray-700">
          HVAC system failures can be frustrating and uncomfortable. Having faced these issues myself, I know how important it is to quickly identify problems and take appropriate action. This guide will help you troubleshoot common HVAC emergencies and understand when it’s time to call a professional.
        </p>

        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold">Common HVAC Emergencies</h2>
          </CardHeader>
          <CardContent>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Complete system shutdown</li>
              <li>Unusual noises or smells</li>
              <li>Inconsistent heating or cooling</li>
              <li>Water leaks around the unit</li>
              <li>Frequent cycling on and off</li>
              <li>Thermostat malfunctions</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <h2 className="text-2xl font-semibold">Troubleshooting Steps</h2>
          </CardHeader>
          <CardContent>
            <ol className="list-decimal list-inside space-y-3 text-gray-700">
              <li>
                <strong>Check the Power Supply:</strong> Ensure the HVAC unit is plugged in and the circuit breaker has not tripped.
              </li>
              <li>
                <strong>Inspect the Thermostat:</strong> Verify the thermostat settings and replace batteries if needed.
              </li>
              <li>
                <strong>Examine Air Filters:</strong> Dirty or clogged filters can restrict airflow; replace or clean filters regularly.
              </li>
              <li>
                <strong>Look for Obstructions:</strong> Check vents and outdoor units for blockages or debris.
              </li>
              <li>
                <strong>Check for Water Leaks:</strong> Inspect condensate drain lines and pans for clogs or leaks.
              </li>
              <li>
                <strong>Listen for Unusual Noises:</strong> Identify any strange sounds that may indicate mechanical issues.
              </li>
            </ol>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h2 className="text-2xl font-semibold">When to Call a Professional</h2>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">
              If troubleshooting does not resolve the issue, or if you notice any of the following, contact a licensed HVAC technician immediately:
            </p>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Electrical burning smells or smoke</li>
              <li>Refrigerant leaks</li>
              <li>Persistent water leaks</li>
              <li>System fails to turn on after resetting breakers</li>
              <li>Unusual noises continue after basic checks</li>
            </ul>
          </CardContent>
        </Card>
      </article>
      <div className="mt-12 text-center">
        <Link
          href={{
            pathname: "/search",
            query: { query: "HVAC emergency troubleshooting" },
          }}
          className="inline-block rounded bg-blue-600 px-6 py-3 text-white font-semibold hover:bg-blue-700 transition"
          aria-label="Search for HVAC emergency services"
        >
          Search for HVAC Emergency Services
        </Link>
      </div>
    </main>
  )
}
