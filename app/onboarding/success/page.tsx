"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { CheckCircle2, Clock, Mail, ArrowRight } from "lucide-react"
import { completeOnboarding } from "@/app/onboarding-actions"

export default function OnboardingSuccessPage() {
  const router = useRouter()
  const { data: session, status } = useSession()

  useEffect(() => {
    // Mark onboarding as complete
    if (session?.user?.id) {
      completeOnboarding(session.user.id)
        .catch((error) => {
          console.error("Error completing onboarding:", error)
        })
    }
  }, [session])

  const handleGoToDashboard = () => {
    router.push("/business/dashboard")
  }

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    router.push("/login")
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto max-w-2xl px-4">
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle2 className="w-12 h-12 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Congratulations! Your Business is Submitted
          </h1>
          <p className="text-lg text-gray-600">
            Thank you for joining Urgent Aid Locator. Your business listing is now under review.
          </p>
        </div>

        <div className="space-y-6">
          {/* Review Process Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-blue-600" />
                What Happens Next?
              </CardTitle>
              <CardDescription>
                Here's what you can expect during the review process
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-sm font-medium text-blue-600">1</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Review Submitted</h4>
                    <p className="text-sm text-gray-600">
                      Your business information has been submitted and is now in our review queue.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-sm font-medium text-blue-600">2</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Admin Review (1-2 Business Days)</h4>
                    <p className="text-sm text-gray-600">
                      Our team will review your business information to ensure it meets our quality standards.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-sm font-medium text-green-600">3</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Go Live!</h4>
                    <p className="text-sm text-gray-600">
                      Once approved, your business will be visible to customers searching for services in your area.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Email Notification Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="w-5 h-5 text-green-600" />
                Stay Updated
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                We'll send you an email notification at <strong>{session?.user?.email}</strong> when:
              </p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-500" />
                  Your business listing is approved and live
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-500" />
                  You receive customer inquiries or reviews
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-500" />
                  Important updates about your listing
                </li>
              </ul>
            </CardContent>
          </Card>

          {/* Dashboard Access Card */}
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader>
              <CardTitle className="text-blue-900">Access Your Dashboard</CardTitle>
              <CardDescription className="text-blue-700">
                While your listing is under review, you can still access your business dashboard
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-blue-700 text-sm mb-4">
                Your dashboard allows you to view your submission status, edit your business information, 
                and prepare for when your listing goes live.
              </p>
              <Button onClick={handleGoToDashboard} className="w-full sm:w-auto">
                Go to Dashboard
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </CardContent>
          </Card>

          {/* Support Card */}
          <Card>
            <CardHeader>
              <CardTitle>Need Help?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm mb-4">
                If you have questions about the review process or need to make changes to your submission, 
                our support team is here to help.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button variant="outline" size="sm">
                  Contact Support
                </Button>
                <Button variant="outline" size="sm">
                  View FAQ
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="text-center mt-12 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Thank you for choosing Urgent Aid Locator to connect with customers in need.
          </p>
        </div>
      </div>
    </div>
  )
}
