"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { useSession } from "next-auth/react"
import { OnboardingWizard, OnboardingStep } from "@/components/onboarding-wizard"
import { BusinessInfoStep } from "@/components/onboarding-steps/business-info-step"
import { CategoriesStep } from "@/components/onboarding-steps/categories-step"
import { HoursStep } from "@/components/onboarding-steps/hours-step"
import { ContactStep } from "@/components/onboarding-steps/contact-step"
import { ReviewStep } from "@/components/onboarding-steps/review-step"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { 
  checkOnboardingStatus, 
  startOnboarding, 
  updateOnboardingStep,
  saveOnboardingProgress 
} from "@/app/onboarding-actions"
import { registerBusiness } from "@/app/auth-actions"

const ONBOARDING_STEPS: OnboardingStep[] = [
  {
    id: "business-info",
    title: "Business Info",
    description: "Tell us about your business",
    component: BusinessInfoStep,
  },
  {
    id: "categories",
    title: "Categories",
    description: "Choose your business categories",
    component: CategoriesStep,
  },
  {
    id: "hours",
    title: "Hours",
    description: "Set your operating hours",
    component: HoursStep,
  },
  {
    id: "contact",
    title: "Contact",
    description: "Provide contact information",
    component: ContactStep,
  },
  {
    id: "review",
    title: "Review",
    description: "Review and submit",
    component: ReviewStep,
  },
]

export default function OnboardingPage() {
  const router = useRouter()
  const { data: session, status } = useSession()
  const [currentStep, setCurrentStep] = useState("business-info")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  
  const [formData, setFormData] = useState({
    businessInfo: {
      name: "",
      description: "",
      yearEstablished: "",
      website: "",
      isEmergencyProvider: true,
      imageUrl: null as string | null,
    },
    categories: {
      primaryCategory: "",
      additionalCategories: [] as string[],
      searchTerms: [] as string[],
    },
    hours: {
      is24Hours: false,
      regularHours: {
        monday: { open: "09:00", close: "17:00", isClosed: false },
        tuesday: { open: "09:00", close: "17:00", isClosed: false },
        wednesday: { open: "09:00", close: "17:00", isClosed: false },
        thursday: { open: "09:00", close: "17:00", isClosed: false },
        friday: { open: "09:00", close: "17:00", isClosed: false },
        saturday: { open: "09:00", close: "17:00", isClosed: true },
        sunday: { open: "09:00", close: "17:00", isClosed: true },
      },
      holidayHours: "",
      emergencyAfterHours: false,
    },
    contact: {
      phone: "",
      email: "",
      address: "",
      city: "",
      state: "",
      zipCode: "",
      serviceRadius: "25",
    },
  })

  // Check onboarding status on mount
  useEffect(() => {
    if (status === "authenticated" && session?.user?.id) {
      checkOnboardingStatus(session.user.id)
        .then((result) => {
          if (result.error) {
            setError(result.error)
          } else if (!result.needsOnboarding) {
            // User doesn't need onboarding, redirect to dashboard
            router.push("/business/dashboard")
            return
          } else {
            // Start onboarding if not started
            if (!result.hasStarted) {
              startOnboarding(session.user.id, "business-info")
            } else if (result.currentStep) {
              setCurrentStep(result.currentStep)
            }
          }
          setIsLoading(false)
        })
        .catch((error) => {
          console.error("Error checking onboarding status:", error)
          setError("Failed to load onboarding status")
          setIsLoading(false)
        })
    } else if (status === "unauthenticated") {
      router.push("/login")
    }
  }, [session, status, router])

  const handleStepChange = async (stepId: string) => {
    setCurrentStep(stepId)
    
    // Save progress to database
    if (session?.user?.id) {
      await updateOnboardingStep(session.user.id, stepId)
      await saveOnboardingProgress(session.user.id, stepId, formData)
    }
  }

  const handleComplete = async () => {
    if (!session?.user?.id) {
      setError("You must be logged in to complete onboarding")
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      // Submit the business registration
      const result = await registerBusiness(session.user.id, {
        businessInfo: formData.businessInfo,
        categories: {
          selectedCategories: [
            formData.categories.primaryCategory,
            ...formData.categories.additionalCategories
          ],
          otherCategory: ""
        },
        hours: {
          schedule: formData.hours.regularHours
        },
        contact: formData.contact
      })

      if (!result.success) {
        setError(result.error || "Failed to submit business registration")
        setIsSubmitting(false)
        return
      }

      // Redirect to success page
      router.push("/onboarding/success")
    } catch (error) {
      console.error("Error completing onboarding:", error)
      setError("An unexpected error occurred. Please try again.")
      setIsSubmitting(false)
    }
  }

  const updateFormData = (updates: any) => {
    setFormData(prev => ({ ...prev, ...updates }))
  }

  if (status === "loading" || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading onboarding...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              {error}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    )
  }

  return (
    <OnboardingWizard
      steps={ONBOARDING_STEPS}
      currentStepId={currentStep}
      onStepChange={handleStepChange}
      onComplete={handleComplete}
      formData={formData}
      updateFormData={updateFormData}
      isSubmitting={isSubmitting}
      title="Welcome to Urgent Aid Locator"
      description="Let's get your business set up so customers can find you when they need help"
    />
  )
}
