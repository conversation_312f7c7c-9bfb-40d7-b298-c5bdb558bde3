'use server'

import { getAllCategories, searchBusinesses } from "@/lib/db-access"
import { Business } from "@/lib/data"
import { prisma } from "@/lib/prisma"
import userPrisma from "@/lib/user-prisma"
import { recordSearch, getRecentSearches, clearRecentSearches, removeRecentSearch, getPopularEmergencyServices, recordBusinessView, getPopularBusinesses } from "@/lib/search-tracking-service"
import { enhancedBusinessSearch } from "@/lib/enhanced-search-service"
import { Logger } from '@logtail/next'

// Reverse geocoding function to get zip code from coordinates
async function reverseGeocode(latitude: number, longitude: number): Promise<string | null> {
  try {
    // Using the free Nominatim API for reverse geocoding
    const response = await fetch(
      "https://nominatim.openstreetmap.org/reverse?format=json&lat=" + latitude + "&lon=" + longitude + "&zoom=18&addressdetails=1",
      {
        headers: {
          'User-Agent': 'EmergencyFind/1.0',
        },
      }
    )

    if (!response.ok) {
      throw new Error("Geocoding API error: " + response.status)
    }

    const data = await response.json()

    // Extract postal code if available
    const postalCode = data.address?.postcode

    return postalCode || null
  } catch (error) {
    console.error('Error in reverse geocoding:', error)
    return null
  }
}

// Server action to get all categories
export async function getCategoriesAction(): Promise<string[]> {
  return await getAllCategories();
}

export async function searchBusinessesAction({
  query,
  location,
  category,
  page,
  pageSize,
  distance,
  open24,
  verified,
  rating,
  sortBy,
}: {
  query?: string
  location?: string
  category?: string
  page?: number
  pageSize?: number
  distance?: number
  open24?: boolean
  verified?: boolean
  rating?: number
  sortBy?: "rating" | "distance"
}): Promise<{
  businesses: Business[]
  totalCount: number
  locationInfo?: {
    zipCode?: string
    formattedLocation?: string
    isApproximate?: boolean
  }
}> {
  const log = new Logger();
  let locationInfo: { zipCode?: string; formattedLocation?: string; isApproximate?: boolean } = {}
  const currentPage = page || 1;
  const itemsPerPage = pageSize || 10; // Default page size
  const offset = (currentPage - 1) * itemsPerPage;
  
  // Handle distance of 0 properly - use a very small value instead of default
  const searchDistance = distance !== undefined ? distance : 10; // Default distance in miles

  let searchResults: { businesses: Business[]; totalCount: number } = { businesses: [], totalCount: 0 };

  // Check if location is coordinates
  if (location && location.includes(',')) {
    try {
      console.log("Processing location coordinates:", location);
      log.info("Processing location coordinates", { location, searchDistance: searchDistance + " miles" });
      console.log("Using distance filter:", searchDistance, "miles");

      const [latStr, lngStr] = location.split(',')
      const latitude = parseFloat(latStr)
      const longitude = parseFloat(lngStr)

      if (!isNaN(latitude) && !isNaN(longitude)) {
        console.log("Valid coordinates:", latitude, longitude);
        log.info("Valid coordinates received", { latitude, longitude });

        // Get zip code from coordinates
        const zipCode = await reverseGeocode(latitude, longitude)
        console.log("Reverse geocoded zip code:", zipCode);
        log.info("Reverse geocoded zip code", { zipCode });

        // Even if we don't get a zip code, we can still use coordinates
        locationInfo.formattedLocation = zipCode || "your current location";
        if (zipCode) {
          locationInfo.zipCode = zipCode;
        }

        // Check if this is an approximate location from IP
        const isApproximate = location.includes('approximate=true');
        if (isApproximate) {
          locationInfo.isApproximate = true;
          locationInfo.formattedLocation = zipCode || "your approximate location";
        }

        // Convert distance from miles to approximate latitude/longitude degrees
        // 1 degree of latitude is approximately 69 miles
        // 1 degree of longitude varies but at 40 degrees latitude it's about 53 miles
        
        // Handle distance of 0 specially - use a very small radius instead of 0
        // This ensures we only get exact matches or very close ones
        const effectiveDistance = searchDistance === 0 ? 0.1 : searchDistance;
        
        const latDelta = effectiveDistance / 69;
        const lngDelta = effectiveDistance / 53;

        console.log(`Using effective search radius: ${effectiveDistance} miles (${latDelta.toFixed(6)} lat degrees, ${lngDelta.toFixed(6)} lng degrees)`);

        // Optimize coordinate filtering by querying with bounding box
        const latMin = latitude - latDelta;
        const latMax = latitude + latDelta;
        const lngMin = longitude - lngDelta;
        const lngMax = longitude + lngDelta;

        // Query businesses within bounding box and other filters
        const whereConditions: any = {
          AND: [
            {
              latitude: {
                gte: latMin,
                lte: latMax,
              },
            },
            {
              longitude: {
                gte: lngMin,
                lte: lngMax,
              },
            },
          ],
        };

        if (query) {
          whereConditions.AND.push({
            OR: [
              { term: { contains: query } },
              { name: { contains: query } },
              { categories: { contains: query } },
            ],
          });
        }

        if (category) {
          // Categories are stored as comma-separated strings like "Category1, Category2, Category3"
          // We need to match the exact category, which could be at the start, middle, or end of the string
          whereConditions.AND.push({
            OR: [
              { categories: { equals: category } },                    // Exact match for single category
              { categories: { startsWith: category + ", " } },         // At the start followed by comma
              { categories: { endsWith: ", " + category } },           // At the end with comma before
              { categories: { contains: ", " + category + ", " } },    // In the middle with commas on both sides
            ],
          });
          console.log("Filtering by category in coordinates search: \"" + category + "\"");
        }

        // Add filter for 24-hour businesses
        if (open24) {
          whereConditions.AND.push({
            OR: [
              { term: { contains: "24" } },
              { term: { contains: "open 24" } },
              { term: { contains: "24/7" } },
              { term: { contains: "after hours" } },
            ],
          });
        }

        // Add filter for verified businesses (change to at least 1 rating)
        if (verified) {
          whereConditions.AND.push({
            reviewCount: {
              gte: 1,
            },
          });
        }

        // Add filter for minimum rating (change back from exact rating)
        if (rating) {
          whereConditions.AND.push({
            rating: {
              gte: rating,
            },
          });
        }

        // Get total count for filtered results
        const totalCount = await prisma.business.count({ where: whereConditions });

        // Get paginated results
        let businesses = await prisma.business.findMany({
          where: whereConditions,
          take: itemsPerPage,
          skip: offset,
          orderBy: sortBy === "rating" ? { rating: "desc" } : undefined,
        });

        // Sort by distance client-side (optional, can be optimized with raw SQL)
        if (sortBy === "distance") {
          businesses = businesses
            .map((b) => ({
              id: b.id,
              name: b.name,
              categories: b.categories ? b.categories.split(", ") : [],
              phone: b.phone || "",
              address: b.address || "",
              city: b.city || "",
              searchTerm: b.term || "",
              url: b.url || "",
              rating: b.rating || 0,
              reviewCount: b.reviewCount || 0,
              isClosed: Boolean(b.isClosed),
              latitude: b.latitude || 0,
              longitude: b.longitude || 0,
              zipCode: b.zipCode || undefined,
              region: b.region || undefined,
              price: b.price || undefined,
              imageUrl: b.imageUrl || undefined,
              is24Hours: b.term ? b.term.includes("24") || b.term.includes("open 24") || b.term.includes("24/7") || b.term.includes("after hours") : false,
              distance: Math.sqrt(
                Math.pow(b.latitude - latitude, 2) + Math.pow(b.longitude - longitude, 2)
              ),
            }))
            .sort((a, b) => a.distance - b.distance)
        }

        // Map businesses to expected format
        const mappedBusinesses = businesses.map((b) => ({
          id: b.id,
          name: b.name,
          categories: b.categories ? b.categories.split(", ") : [],
          phone: b.phone || "",
          address: b.address || "",
          city: b.city || "",
          searchTerm: b.term || "",
          url: b.url || "",
          rating: b.rating || 0,
          reviewCount: b.reviewCount || 0,
          isClosed: Boolean(b.isClosed),
          latitude: b.latitude || 0,
          longitude: b.longitude || 0,
          zipCode: b.zipCode || undefined,
          region: b.region || undefined,
          price: b.price || undefined,
          imageUrl: b.imageUrl || undefined,
          is24Hours: b.term ? b.term.includes("24") || b.term.includes("open 24") || b.term.includes("24/7") || b.term.includes("after hours") : false,
        }))

        console.log(`Total count for coordinate search: ${totalCount}`)
        log.info("Coordinate search completed", { totalCount, businessesFound: mappedBusinesses.length });

        searchResults = {
          businesses: mappedBusinesses,
          totalCount,
        }

      } else {
        console.error("Invalid coordinates format:", latStr, lngStr)
        log.error("Invalid coordinates format", { latStr, lngStr });
        // If coordinates invalid, return empty result to avoid hanging
        searchResults = {
          businesses: [],
          totalCount: 0,
        }
      }
    } catch (error) {
      console.error("Error processing coordinates:", error)
      log.error("Error processing coordinates", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      // Return empty result on error to avoid hanging
      searchResults = {
        businesses: [],
        totalCount: 0,
      }
    }
  } else {
    // Use enhanced search that combines main database and registered businesses
    searchResults = await enhancedBusinessSearch({
      query,
      location,
      category,
      coordinates: undefined,
      radius: searchDistance,
      limit: itemsPerPage,
      offset,
    })

    console.log(`Total count for enhanced search: ${searchResults.totalCount}`)
  }

  // Use the combined results from enhanced search
  const combinedBusinesses = searchResults.businesses;

  // After fetching results, sort businesses to push zero or null ratings to bottom
  if (sortBy === "rating" && combinedBusinesses.length > 0) {
    combinedBusinesses.sort((a, b) => {
      const ratingA = a.rating || 0;
      const ratingB = b.rating || 0;
      if (ratingA === 0 && ratingB === 0) return 0;
      if (ratingA === 0) return 1;
      if (ratingB === 0) return -1;
      return ratingB - ratingA;
    });
  }

  // Record the search for analytics and recent searches
  // Only record if we have a meaningful query or location
  // Add a simple check to prevent duplicate recordings within the same request
  if (query || location) {
    try {
      console.log(`Recording search: "${query}" with location: "${location || 'N/A'}"`);
      log.info("Recording search", { query: query || '', location: location || 'N/A' });
      await recordSearch({
        userId: undefined, // TODO: Get userId from session when auth is implemented
        searchQuery: query || '',
        searchLocation: location,
      });
    } catch (error) {
      console.error('Error recording search:', error);
      log.error('Error recording search', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      // Don't fail the search if tracking fails
    }
  }

  await log.flush();

  return {
    businesses: combinedBusinesses,
    totalCount: searchResults.totalCount,
    locationInfo,
  }
}

// Server actions for recent searches
export async function getRecentSearchesAction(userId?: string) {
  const log = new Logger();
  try {
    log.info("getRecentSearchesAction - starting", { userId });

    // During build time (static generation), return empty array to avoid database connection issues
    if (process.env.NODE_ENV === 'production' && process.env.VERCEL_ENV === 'production') {
      log.info("getRecentSearchesAction - build time, returning empty array");
      await log.flush();
      return [];
    }

    // Check database connection first
    try {
      const dbStatus = await userPrisma.$queryRaw`SELECT 1 as status`;
      log.info("getRecentSearchesAction - database connection check", {
        status: "connected",
        databaseUrl: process.env.USER_DATABASE_URL
      });
    } catch (dbError) {
      log.error("getRecentSearchesAction - database connection failed", {
        error: dbError instanceof Error ? dbError.message : String(dbError),
        databaseUrl: process.env.USER_DATABASE_URL
      });
      throw dbError; // Re-throw to be caught by the outer try/catch
    }
    
    const result = await getRecentSearches(userId, 3);
    log.info("getRecentSearchesAction - completed", { 
      count: result.length,
      sample: result.length > 0 ? result[0].query : 'none' 
    });
    await log.flush();
    return result;
  } catch (error) {
    log.error("getRecentSearchesAction - error", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      databaseUrl: process.env.USER_DATABASE_URL
    });
    await log.flush();
    return [];
  }
}

export async function clearRecentSearchesAction(userId: string) {
  return await clearRecentSearches(userId);
}

export async function removeRecentSearchAction(userId: string, searchId: string) {
  return await removeRecentSearch(userId, searchId);
}

// Server action for recording business views
export async function recordBusinessViewAction(businessId: string, businessName: string, userId?: string) {
  const log = new Logger();
  try {
    console.log("recordBusinessViewAction - starting", { businessId, businessName, userId });
    log.info("recordBusinessViewAction - starting", { businessId, businessName, userId });

    const result = await recordBusinessView({ userId, businessId, businessName });
    console.log("recordBusinessViewAction - completed", { businessId, businessName });
    log.info("recordBusinessViewAction - completed", { businessId, businessName });
    await log.flush();
    return result;
  } catch (error) {
    console.error("recordBusinessViewAction - error", {
      error: error instanceof Error ? error.message : String(error),
      businessId,
      businessName,
      userId
    });
    log.error("recordBusinessViewAction - error", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      businessId,
      businessName,
      userId
    });
    await log.flush();
    throw error;
  }
}

// Server action for popular businesses (based on views, not search terms)
export async function getPopularBusinessesAction(limit: number = 5) {
  const log = new Logger();
  try {
    console.log("getPopularBusinessesAction - starting", { limit });
    log.info("getPopularBusinessesAction - starting", { limit });

    // Check database connection first
    try {
      const dbStatus = await userPrisma.$queryRaw`SELECT 1 as status`;
      console.log("getPopularBusinessesAction - database connection check", {
        status: "connected",
        databaseUrl: process.env.USER_DATABASE_URL
      });
      log.info("getPopularBusinessesAction - database connection check", {
        status: "connected",
        databaseUrl: process.env.USER_DATABASE_URL
      });
    } catch (dbError) {
      console.error("getPopularBusinessesAction - database connection failed", {
        error: dbError instanceof Error ? dbError.message : String(dbError),
        databaseUrl: process.env.USER_DATABASE_URL
      });
      log.error("getPopularBusinessesAction - database connection failed", {
        error: dbError instanceof Error ? dbError.message : String(dbError),
        databaseUrl: process.env.USER_DATABASE_URL
      });
      throw dbError; // Re-throw to be caught by the outer try/catch
    }

    const result = await getPopularBusinesses(limit);
    console.log("getPopularBusinessesAction - completed", {
      count: result.length,
      sample: result.length > 0 ? result[0].businessName : 'none'
    });
    log.info("getPopularBusinessesAction - completed", {
      count: result.length,
      sample: result.length > 0 ? result[0].businessName : 'none'
    });
    await log.flush();
    return result;
  } catch (error) {
    console.error("getPopularBusinessesAction - error", {
      error: error instanceof Error ? error.message : String(error),
      limit
    });
    log.error("getPopularBusinessesAction - error", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      limit
    });
    await log.flush();
    throw error;
  }
}

// Server action for popular emergency services (no caching for fresh data)
export async function getPopularEmergencyServicesAction(limit: number = 5) {
  const log = new Logger();
  try {
    console.log("getPopularEmergencyServicesAction - starting", { limit });
    log.info("getPopularEmergencyServicesAction - starting", { limit });

    // During build time (static generation), return empty array to avoid database connection issues
    if (process.env.NODE_ENV === 'production' && process.env.VERCEL_ENV === 'production') {
      console.log("getPopularEmergencyServicesAction - build time, returning empty array");
      log.info("getPopularEmergencyServicesAction - build time, returning empty array");
      await log.flush();
      return [];
    }

    // Check database connection first
    try {
      const dbStatus = await userPrisma.$queryRaw`SELECT 1 as status`;
      console.log("getPopularEmergencyServicesAction - database connection check", {
        status: "connected",
        databaseUrl: process.env.USER_DATABASE_URL
      });
      log.info("getPopularEmergencyServicesAction - database connection check", {
        status: "connected",
        databaseUrl: process.env.USER_DATABASE_URL
      });
    } catch (dbError) {
      console.error("getPopularEmergencyServicesAction - database connection failed", {
        error: dbError instanceof Error ? dbError.message : String(dbError),
        databaseUrl: process.env.USER_DATABASE_URL
      });
      log.error("getPopularEmergencyServicesAction - database connection failed", {
        error: dbError instanceof Error ? dbError.message : String(dbError),
        databaseUrl: process.env.USER_DATABASE_URL
      });

      // During build or if database is not available, return empty array instead of throwing
      console.log("getPopularEmergencyServicesAction - returning empty array due to database error");
      log.info("getPopularEmergencyServicesAction - returning empty array due to database error");
      await log.flush();
      return [];
    }

    const result = await getPopularEmergencyServices(limit);
    console.log("getPopularEmergencyServicesAction - completed", {
      count: result.length,
      sample: result.length > 0 ? result[0].searchTerm : 'none'
    });
    log.info("getPopularEmergencyServicesAction - completed", {
      count: result.length,
      sample: result.length > 0 ? result[0].searchTerm : 'none'
    });
    await log.flush();
    return result;
  } catch (error) {
    log.error("getPopularEmergencyServicesAction - error", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      databaseUrl: process.env.USER_DATABASE_URL
    });
    await log.flush();
    return [];
  }
}
