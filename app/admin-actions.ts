'use server'

import userPrisma from '@/lib/user-prisma'
import { requireAdmin } from '@/lib/admin-auth'
import { revalidatePath } from 'next/cache'

// Get all pending businesses for review
export async function getPendingBusinesses() {
  await requireAdmin()
  
  try {
    const businesses = await userPrisma.registeredBusiness.findMany({
      where: {
        status: "PENDING_REVIEW"
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
    
    return businesses
  } catch (error) {
    console.error('Error fetching pending businesses:', error)
    throw new Error('Failed to fetch pending businesses')
  }
}

// Get all businesses with any status for admin dashboard
export async function getAllBusinessesForAdmin() {
  await requireAdmin()
  
  try {
    const businesses = await userPrisma.registeredBusiness.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    })
    
    return businesses
  } catch (error) {
    console.error('Error fetching businesses for admin:', error)
    throw new Error('Failed to fetch businesses')
  }
}

// Get business by ID for review
export async function getBusinessForReview(businessId: string) {
  await requireAdmin()
  
  try {
    const business = await userPrisma.registeredBusiness.findUnique({
      where: { id: businessId }
    })
    
    if (!business) {
      throw new Error('Business not found')
    }
    
    return business
  } catch (error) {
    console.error('Error fetching business for review:', error)
    throw new Error('Failed to fetch business')
  }
}

// Approve a business
export async function approveBusiness(businessId: string, adminNotes?: string) {
  const admin = await requireAdmin()
  
  try {
    const updatedBusiness = await userPrisma.registeredBusiness.update({
      where: { id: businessId },
      data: {
        status: "APPROVED",
        reviewedBy: admin.id,
        reviewedAt: new Date(),
        adminNotes: adminNotes || null,
        rejectionReason: null // Clear any previous rejection reason
      }
    })
    
    revalidatePath('/admin/dashboard')
    revalidatePath(`/admin/review/${businessId}`)
    
    return { success: true, business: updatedBusiness }
  } catch (error) {
    console.error('Error approving business:', error)
    return { success: false, error: 'Failed to approve business' }
  }
}

// Reject a business
export async function rejectBusiness(businessId: string, rejectionReason: string, adminNotes?: string) {
  const admin = await requireAdmin()
  
  try {
    const updatedBusiness = await userPrisma.registeredBusiness.update({
      where: { id: businessId },
      data: {
        status: "REJECTED",
        reviewedBy: admin.id,
        reviewedAt: new Date(),
        adminNotes: adminNotes || null,
        rejectionReason
      }
    })
    
    revalidatePath('/admin/dashboard')
    revalidatePath(`/admin/review/${businessId}`)
    
    return { success: true, business: updatedBusiness }
  } catch (error) {
    console.error('Error rejecting business:', error)
    return { success: false, error: 'Failed to reject business' }
  }
}

// Request changes for a business
export async function requestBusinessChanges(businessId: string, adminNotes: string) {
  const admin = await requireAdmin()
  
  try {
    const updatedBusiness = await userPrisma.registeredBusiness.update({
      where: { id: businessId },
      data: {
        status: "NEEDS_CHANGES",
        reviewedBy: admin.id,
        reviewedAt: new Date(),
        adminNotes,
        rejectionReason: null
      }
    })
    
    revalidatePath('/admin/dashboard')
    revalidatePath(`/admin/review/${businessId}`)
    
    return { success: true, business: updatedBusiness }
  } catch (error) {
    console.error('Error requesting business changes:', error)
    return { success: false, error: 'Failed to request changes' }
  }
}

// Get business review statistics
export async function getBusinessReviewStats() {
  await requireAdmin()
  
  try {
    const stats = await userPrisma.registeredBusiness.groupBy({
      by: ['status'],
      _count: {
        status: true
      }
    })
    
    const formattedStats = {
      pending: stats.find(s => s.status === 'PENDING_REVIEW')?._count.status || 0,
      approved: stats.find(s => s.status === 'APPROVED')?._count.status || 0,
      rejected: stats.find(s => s.status === 'REJECTED')?._count.status || 0,
      needsChanges: stats.find(s => s.status === 'NEEDS_CHANGES')?._count.status || 0,
      total: stats.reduce((sum, s) => sum + s._count.status, 0)
    }
    
    return formattedStats
  } catch (error) {
    console.error('Error fetching business review stats:', error)
    throw new Error('Failed to fetch statistics')
  }
}
