"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Search, ArrowRight } from "lucide-react"

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState("")

  // FAQ categories and questions
  const faqCategories = [
    {
      id: "general",
      name: "General Questions",
      questions: [
        {
          id: "what-is",
          question: "What is EmergencyFind?",
          answer:
            "EmergencyFind is a platform that connects users with verified 24/7 emergency service providers across multiple categories. Our mission is to help people quickly find reliable emergency services when they need them most.",
        },
        {
          id: "how-works",
          question: "How does EmergencyFind work?",
          answer:
            "Users can search for emergency services by category, location, or specific need. Our platform displays available service providers with important information like ratings, availability, and contact details. Users can then directly contact the service provider of their choice.",
        },
        {
          id: "service-areas",
          question: "What areas do you serve?",
          answer:
            "EmergencyFind currently serves major metropolitan areas across the United States, with a focus on Colorado. We're continuously expanding our coverage to include more locations and service providers.",
        },
      ],
    },
    {
      id: "services",
      name: "Services & Providers",
      questions: [
        {
          id: "types-services",
          question: "What types of emergency services can I find?",
          answer:
            "Our platform includes a wide range of emergency services, including plumbers, locksmiths, electricians, HVAC technicians, towing services, emergency notaries, and more. All service providers in our directory offer emergency or after-hours services.",
        },
        {
          id: "provider-verification",
          question: "How do you verify service providers?",
          answer:
            "We have a thorough verification process that includes checking business licenses, insurance coverage, service history, and customer reviews. We also require providers to commit to our service standards, including emergency availability and transparent pricing.",
        },
        {
          id: "service-guarantee",
          question: "Do you guarantee the services provided?",
          answer:
            "While we carefully vet all service providers, EmergencyFind is a directory service that connects users with providers. The contract for service is between you and the service provider. However, we do take customer feedback seriously and may remove providers who consistently fail to meet our standards.",
        },
      ],
    },
    {
      id: "using",
      name: "Using EmergencyFind",
      questions: [
        {
          id: "cost-use",
          question: "Does it cost anything to use EmergencyFind?",
          answer:
            "No, EmergencyFind is completely free for users to search and connect with emergency service providers. Service providers pay a subscription fee to be listed in our directory.",
        },
        {
          id: "account-needed",
          question: "Do I need an account to use EmergencyFind?",
          answer:
            "No, you can search for and contact emergency service providers without creating an account. However, creating a free account allows you to save favorite providers, view your search history, and leave reviews.",
        },
        {
          id: "contact-provider",
          question: "How do I contact a service provider?",
          answer:
            "Each service provider listing includes direct contact information, typically a phone number. For emergency services, we recommend calling the provider directly for the fastest response. Some providers may also offer online booking for non-urgent situations.",
        },
      ],
    },
    {
      id: "providers",
      name: "For Service Providers",
      questions: [
        {
          id: "become-provider",
          question: "How can I list my business on EmergencyFind?",
          answer:
            "Service providers can register through our 'List Your Business' page. You'll need to provide business information, service details, availability, and contact information. Our team will review your application and may request additional verification documents.",
        },
        {
          id: "provider-requirements",
          question: "What are the requirements for service providers?",
          answer:
            "To be listed on EmergencyFind, service providers must: 1) Offer emergency or after-hours services, 2) Have proper business licensing and insurance, 3) Commit to transparent pricing, 4) Maintain a professional standard of service, and 5) Respond promptly to service requests.",
        },
        {
          id: "provider-cost",
          question: "How much does it cost to list my business?",
          answer:
            "We offer several subscription tiers for service providers, starting at $49/month. The cost varies based on your service category, location, and the features you need. Visit our 'List Your Business' page for current pricing information.",
        },
      ],
    },
  ]

  // Filter questions based on search query
  const filteredFAQs = searchQuery
    ? faqCategories
        .map((category) => ({
          ...category,
          questions: category.questions.filter(
            (q) =>
              q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
              q.answer.toLowerCase().includes(searchQuery.toLowerCase()),
          ),
        }))
        .filter((category) => category.questions.length > 0)
    : faqCategories

  return (
    <main className="container mx-auto max-w-4xl px-4 py-12">
      <div className="mb-8 text-center">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">Frequently Asked Questions</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Find answers to common questions about EmergencyFind and our services.
        </p>
      </div>

      {/* Search */}
      <div className="mb-12 max-w-2xl mx-auto">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <Input
            placeholder="Search questions..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* FAQ Content */}
      <div className="space-y-8">
        {filteredFAQs.map((category) => (
          <div key={category.id} className={category.questions.length === 0 ? "hidden" : ""}>
            <h2 className="text-2xl font-bold mb-4">{category.name}</h2>
            <Accordion type="single" collapsible className="mb-6">
              {category.questions.map((item) => (
                <AccordionItem key={item.id} value={item.id}>
                  <AccordionTrigger className="text-left font-medium">{item.question}</AccordionTrigger>
                  <AccordionContent className="text-gray-600">{item.answer}</AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        ))}

        {filteredFAQs.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">No questions found matching "{searchQuery}"</p>
            <Button variant="outline" onClick={() => setSearchQuery("")}>
              Clear Search
            </Button>
          </div>
        )}
      </div>

      {/* Contact Section */}
      <Card className="mt-12">
        <CardContent className="p-6">
          <div className="text-center">
            <h2 className="text-xl font-bold mb-2">Still have questions?</h2>
            <p className="text-gray-600 mb-6">
              If you couldn't find the answer to your question, feel free to contact our support team.
            </p>
            <Button asChild>
              <a href="/contact" className="flex items-center justify-center">
                Contact Support
                <ArrowRight className="ml-2 h-4 w-4" />
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </main>
  )
}
