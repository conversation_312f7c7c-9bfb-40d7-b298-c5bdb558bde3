import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { HeartHandshake, Search, ShieldCheck } from "lucide-react";

export default function About() {
  return (
    <main className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-blue-600 py-12 px-4">
        <div className="container mx-auto max-w-6xl text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            About Emergency Find
          </h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Your trusted companion for quickly locating essential aid and
            emergency services when you need them most.
          </p>
        </div>
      </div>

      <div className="container mx-auto max-w-6xl px-4 py-12">
        <Card className="mb-8 shadow-lg">
          <CardHeader>
            <CardTitle className="text-2xl font-semibold text-center md:text-left">
              Our Mission
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 text-lg leading-relaxed">
              Welcome to Emergency Find! Our core mission is to empower you by
              providing a fast, reliable, and easy-to-use platform for
              locating urgent care facilities and other critical aid services.
              We understand that in times of need, quick access to information
              can make all the difference.
            </p>
          </CardContent>
        </Card>

        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center text-xl font-semibold">
                <HeartHandshake className="h-6 w-6 mr-2 text-blue-600" />
                What We Do
              </CardTitle>
            </CardHeader>
            <CardContent className="text-gray-700 space-y-3">
              <p>
                In critical moments, every second counts. Emergency Find is
                designed to:
              </p>
              <ul className="list-disc list-inside space-y-2 pl-4">
                <li>
                  <strong>Locate Nearby Services:</strong> Instantly find urgent
                  care centers, pharmacies, and other relevant aid providers.
                </li>
                <li>
                  <strong>Provide Essential Information:</strong> Access key
                  details like operating hours, contact info, and services.
                </li>
                <li>
                  <strong>Save Important Locations:</strong> Personalize your
                  experience by saving frequently visited or preferred spots.
                </li>
                <li>
                  <strong>Offer Peace of Mind:</strong> Simplifying your search
                  for help, so you can focus on what truly matters.
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center text-xl font-semibold">
                <ShieldCheck className="h-6 w-6 mr-2 text-green-600" />
                Our Commitment
              </CardTitle>
            </CardHeader>
            <CardContent className="text-gray-700 space-y-3">
              <p>
                We are dedicated to maintaining a user-friendly and accurate
                platform. Our goal is to ensure you can find the information
                you need, when you need it.
              </p>
              <p className="font-semibold text-red-600">
                Important: While Emergency Find is a powerful tool, please
                remember that in life-threatening emergencies, you should
                always call your local emergency services number (e.g., 911,
                112) immediately.
              </p>
              <p>Thank you for choosing Emergency Find.</p>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <Card className="bg-blue-50 border-blue-100 shadow-md">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4 text-gray-800">
                Ready to Find Help?
              </h2>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                Explore our categories or use our search feature to find
                specific services available in your area.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg">
                  <Link href="/categories">
                    <Search className="h-5 w-5 mr-2" />
                    Browse Categories
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg">
                  <Link href="/search">
                    Search All Services
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  );
}
