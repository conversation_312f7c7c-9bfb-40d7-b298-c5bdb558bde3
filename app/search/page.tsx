"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams } from "next/navigation" // Import useSearchParams
import Link from "next/link"
import { SearchFilters } from "@/components/search-filters"
import { PaginatedSearchResults } from "@/components/paginated-search-results"
import { SearchMap } from "@/components/search-map"
import { Search } from "@/components/search"
import { SearchLoading } from "@/components/search-loading"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { List, MapPin, AlertCircle, ChevronRight, Home } from "lucide-react"
import { searchBusinessesAction } from "@/app/actions"
import { SearchResultsData } from "@/lib/data"

export default function SearchPageClientWrapper() { // Remove searchParams prop
  const [searchData, setSearchData] = useState<SearchResultsData | null>(null)
  const [loading, setLoading] = useState(false)
  const [sortBy, setSortBy] = useState<"rating" | "distance">("rating")

  const searchParams = useSearchParams() // Use the hook

  const query = (searchParams.get("q") as string) || ""
  const location = (searchParams.get("location") as string) || ""
  const category = (searchParams.get("category") as string) || ""
  const page = searchParams.get("page") ? parseInt(searchParams.get("page") as string) : 1
  const pageSize = searchParams.get("pageSize") ? parseInt(searchParams.get("pageSize") as string) : 10

  // Parse filter parameters
  const distance = searchParams.get("distance") ? parseInt(searchParams.get("distance") as string) : undefined
  const open24 = searchParams.get("open24") === "true"
  const verified = searchParams.get("verified") === "true"
  const rating = searchParams.get("rating") ? parseInt(searchParams.get("rating") as string) : undefined

  useEffect(() => {
    async function fetchData() {
      setLoading(true)
      try {
        const data = await searchBusinessesAction({
          query,
          location,
          category,
          page,
          pageSize,
          distance,
          open24,
          verified,
          rating,
          sortBy,
        })
        setSearchData(data)
      } catch (error) {
        console.error("Error fetching search data:", error)
      } finally {
        setLoading(false)
      }
    }
    fetchData()
  }, [query, location, category, page, pageSize, distance, open24, verified, rating, sortBy])

  // Get location info if we have coordinates
  const [locationInfo, setLocationInfo] = useState<{ zipCode?: string; formattedLocation?: string; isApproximate?: boolean } | undefined>(undefined)
  useEffect(() => {
    async function fetchLocationInfo() {
      if (location && location.includes(",")) {
        try {
          const result = await searchBusinessesAction({
            location,
            page,
            pageSize,
            distance,
            open24,
            verified,
            rating,
            sortBy,
          })
          setLocationInfo(result.locationInfo)
        } catch (error) {
          console.error("Error getting location info:", error)
        }
      }
    }
    fetchLocationInfo()
  }, [location, page, pageSize])

  if (loading || !searchData) {
    return (
      <div className="container mx-auto max-w-6xl px-4 py-6">
        <SearchLoading />
      </div>
    )
  }

  return (
    <main className="min-h-screen bg-gray-50 overflow-x-hidden">
      <div className="bg-blue-600 py-6 px-4">
        <div className="container mx-auto max-w-6xl">
          <Search defaultQuery={query} defaultLocation={location} />
        </div>
      </div>

      <div className="container mx-auto max-w-6xl px-4 py-6">
        {/* Breadcrumb Navigation */}
        {category && (
          <nav className="flex items-center space-x-1 text-sm text-gray-500 mb-6">
            <Link href="/" className="hover:text-gray-700 flex items-center">
              <Home className="h-4 w-4" />
            </Link>
            <ChevronRight className="h-4 w-4" />
            <Link href="/categories" className="hover:text-gray-700">
              Categories
            </Link>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-900 font-medium">{category}</span>
          </nav>
        )}

        <div className="flex flex-col md:flex-row gap-6">
          {/* Filters Section */}
          <div className="w-full md:w-64 shrink-0">
            <SearchFilters
              category={category}
              initialDistance={distance}
              initialOpen24Hours={open24}
              initialVerified={verified}
              initialRating={rating}
            />
          </div>

          {/* Results Section */}
          <div className="flex-1 min-w-0">
            <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
              <div className="flex flex-col mb-4">
                <div className="flex justify-between items-center flex-wrap">
                  <h1 className="text-xl font-bold">
                    {category && !query
                      ? `${category} Services`
                      : query
                        ? `Results for "${query}"`
                        : "All Emergency Services"}
                    {location ? ` near ${
                      location.includes(",")
                        ? locationInfo?.isApproximate
                          ? "your approximate location"
                          : "your current location"
                        : location
                    }` : ""}
                  </h1>
                  {category && !query && (
                    <p className="text-gray-600 mt-2">
                      Find reliable {category.toLowerCase()} services available 24/7 for your emergency needs.
                    </p>
                  )}
                  <div className="w-auto">
                  <div className="flex items-center space-x-4">
                    <label htmlFor="sort" className="text-sm font-medium text-gray-700">
                      Sort by:
                    </label>
                    <select
                      id="sort"
                      name="sort"
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value as "rating" | "distance")}
                      className="rounded-md border border-gray-300 bg-white py-1.5 px-3 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                      <option value="rating">Rating</option>
                      <option value="distance" disabled={!location.includes(",")}>
                        Distance
                      </option>
                    </select>
                  </div>
                  </div>
                </div>

                {/* Show warning for approximate location */}
                {locationInfo?.isApproximate && (
                  <div className="mt-2 text-amber-600 bg-amber-50 p-2 rounded-md text-sm flex items-center">
                    <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>
                      Using your approximate location based on your IP address. Results may be less accurate.
                      For more precise results, please allow location access or enter a specific location.
                    </span>
                  </div>
                )}
              </div>

              <Tabs defaultValue="list">
                <TabsContent value="list" className="overflow-x-auto">
                <PaginatedSearchResults
                  initialBusinesses={searchData.businesses}
                  initialTotalCount={searchData.totalCount}
                  initialPage={page}
                  pageSize={pageSize}
                  query={query}
                  location={location}
                  category={category}
                  distance={distance}
                  open24={open24}
                  verified={verified}
                  rating={rating}
                  sortBy={sortBy}
                />
                </TabsContent>
                <TabsContent value="map">
                  <Suspense fallback={<SearchLoading />}>
                    <SearchMap
                      query={query}
                      location={location}
                      category={category}
                      distance={distance}
                      open24={open24}
                      verified={verified}
                      rating={rating}
                    />
                  </Suspense>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
