import { Suspense } from "react"
import { <PERSON><PERSON><PERSON> } from "next"
import { SearchPageClient } from "@/components/search-page-client"
import { generateSearchMetadata } from "@/lib/seo-utils"

interface SearchPageProps {
  searchParams: {
    q?: string
    location?: string
    category?: string
    page?: string
    pageSize?: string
    distance?: string
    open24?: string
    verified?: string
    rating?: string
  }
}

export async function generateMetadata({ searchParams }: SearchPageProps): Promise<Metadata> {
  const query = searchParams.q || ""
  const location = searchParams.location || ""
  const category = searchParams.category || ""

  return generateSearchMetadata(query, location, category)
}

export default function SearchPage({ searchParams }: SearchPageProps) {
  return (
    <Suspense fallback={<div>Loading search...</div>}>
      <SearchPageClient />
    </Suspense>
  )
}
