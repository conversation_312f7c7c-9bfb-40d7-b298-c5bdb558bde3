import Link from "next/link"
import { Search } from "@/components/search"
import { ServiceCategories } from "@/components/service-categories"
import { RecentSearches } from "@/components/recent-searches"
import { PopularServices } from "@/components/popular-services"
import { Button } from "@/components/ui/button"
import { Clock, Shield, MapPin } from "lucide-react"
import { HomeButtons } from "@/components/home-buttons"
export default async function Home() {
  try {
    console.log("Home page - starting to render");
    
    return (
      <main className="min-h-screen">
        {/* Hero Section */}
        <section className="bg-blue-600 py-16 px-4">
          <div className="container mx-auto max-w-6xl text-center">
            <h1 className="text-3xl md:text-5xl font-bold mb-6 text-white">
              Find Emergency Services Near You
            </h1>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Connect with reliable emergency service providers available 24/7 in your area
            </p>
            <div className="max-w-4xl mx-auto">
              <Search defaultQuery="" defaultLocation="" />
            </div>
            <div className="mt-8">
              <HomeButtons />
            </div>
          </div>
        </section>

        {/* Service Categories */}
        <section className="py-12 px-4 bg-white">
          <div className="container mx-auto max-w-6xl">
            <h2 className="text-2xl md:text-3xl font-bold mb-8 text-gray-900">Emergency Service Categories</h2>
            <ServiceCategories />
          </div>
        </section>

        {/* Recent Searches & Popular Services */}
        <section className="py-12 px-4 bg-gray-50">
          <div className="container mx-auto max-w-6xl">
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h2 className="text-2xl font-bold mb-6 text-gray-900">Recent Searches</h2>
                <RecentSearches />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-6 text-gray-900">Popular Emergency Services</h2>
                <PopularServices />
              </div>
            </div>
          </div>
        </section>
        
        {/* Trust Indicators */}
        <section className="py-12 px-4 bg-white">
          <div className="container mx-auto max-w-6xl">
            <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center text-gray-900">
              Why Choose Our Emergency Directory
            </h2>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-gray-50 p-6 rounded-lg text-center">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Clock className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-2">24/7 Availability</h3>
                <p className="text-gray-600">All listed services are available around the clock for emergencies</p>
              </div>
              <div className="bg-gray-50 p-6 rounded-lg text-center">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MapPin className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-2">Local Providers</h3>
                <p className="text-gray-600">Find emergency services in your area with real-time availability</p>
              </div>
              <div className="bg-gray-50 p-6 rounded-lg text-center">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-2">Verified Providers</h3>
                <p className="text-gray-600">All emergency services are verified for reliability and quality</p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-12 px-4 bg-blue-600 text-white">
          <div className="container mx-auto max-w-6xl text-center">
            <h2 className="text-2xl md:text-3xl font-bold mb-4">Are you an emergency service provider?</h2>
            <p className="text-xl text-blue-100 mb-6 max-w-2xl mx-auto">
              List your business in our directory to reach customers when they need you most
            </p>
            <Link href="/business/register">
              <Button size="lg" variant="secondary" className="font-semibold">
                Register Your Business
              </Button>
            </Link>
          </div>
        </section>
      </main>
    )
  } catch (error) {
    console.error("Home page - error rendering", error);
    
    // Return a simplified page if there's an error
    return (
      <main className="min-h-screen">
        {/* Hero Section */}
        <section className="bg-blue-600 py-16 px-4">
          <div className="container mx-auto max-w-6xl text-center">
            <h1 className="text-3xl md:text-5xl font-bold mb-6 text-white">
              Find Emergency Services Near You
            </h1>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Connect with reliable emergency service providers available 24/7 in your area
            </p>
            <div className="max-w-4xl mx-auto">
              <div className="bg-white/20 h-12 rounded-md animate-pulse"></div>
            </div>
          </div>
        </section>
        
        {/* Error message */}
        <section className="py-12 px-4 bg-white">
          <div className="container mx-auto max-w-6xl">
            <h2 className="text-2xl md:text-3xl font-bold mb-8 text-gray-900">Emergency Service Categories</h2>
            <ServiceCategories />
          </div>
        </section>
        
        <section className="py-12 px-4 bg-gray-50">
          <div className="container mx-auto max-w-6xl">
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h2 className="text-2xl font-bold mb-6 text-gray-900">Recent Searches</h2>
                <div className="bg-gray-100 rounded-lg p-6 text-center">
                  <p className="text-gray-500">Unable to load recent searches</p>
                </div>
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-6 text-gray-900">Popular Emergency Services</h2>
                <div className="bg-gray-100 rounded-lg p-6 text-center">
                  <p className="text-gray-500">Unable to load popular services</p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
    )
  }
}
