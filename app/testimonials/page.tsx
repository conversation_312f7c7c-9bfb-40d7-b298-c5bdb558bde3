import { Suspense } from 'react'
import { TestimonialsContent } from './testimonials-content'
import { TestimonialsLoading } from './loading-component'

export default function TestimonialsPage() {
  return (
    <main className="container mx-auto max-w-6xl px-4 py-12">
      <div className="mb-8 text-center">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">Customer Testimonials</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Read what our customers have to say about their experiences with our emergency services.
        </p>
      </div>
      
      {/* Wrap the component that uses useSearchParams in a Suspense boundary */}
      <Suspense fallback={<TestimonialsLoading />}>
        <TestimonialsContent />
      </Suspense>
    </main>
  )
}
