"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export function TestimonialsLoading() {
  return (
    <div className="space-y-8">
      {/* Skeleton for category filters */}
      <div className="flex flex-wrap gap-2 mb-8 justify-center">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-10 w-28 mb-2" />
        ))}
      </div>
      
      {/* Skeleton for testimonials */}
      <div className="grid gap-6 md:grid-cols-2">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="space-y-2">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-24" />
                </div>
                <Skeleton className="h-6 w-28" />
              </div>
              
              <Skeleton className="h-4 w-32 mb-4" />
              
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
