"use client"

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Star } from "lucide-react"

// Sample testimonial data
const testimonials = [
  {
    id: '1',
    name: '<PERSON>',
    service: 'Emergency Plumbing',
    rating: 5,
    date: '2023-10-15',
    content: 'I had a major pipe burst at 2 AM and was in a complete panic. The emergency plumber arrived within 30 minutes and fixed the issue quickly. Saved me from major water damage!',
    location: 'Denver, CO',
    category: 'plumbing'
  },
  {
    id: '2',
    name: '<PERSON>',
    service: '24/7 Locksmith',
    rating: 5,
    date: '2023-09-22',
    content: 'Locked myself out of my apartment at midnight. The locksmith was professional, quick, and got me back inside without damaging my door. Reasonable emergency rates too!',
    location: 'Boulder, CO',
    category: 'locksmith'
  },
  {
    id: '3',
    name: '<PERSON>',
    service: 'Emergency Electrician',
    rating: 4,
    date: '2023-11-05',
    content: 'Had a power outage in half my house. The electrician identified the issue quickly and fixed it the same day. Very knowledgeable and explained everything clearly.',
    location: 'Aurora, CO',
    category: 'electrician'
  },
  {
    id: '4',
    name: '<PERSON>',
    service: 'Emergency HVAC Repair',
    rating: 5,
    date: '2023-12-18',
    content: 'Our heating system failed during the coldest night of the year. The technician came within hours and got our heat back on. The whole family was grateful!',
    location: 'Lakewood, CO',
    category: 'hvac'
  },
  {
    id: '5',
    name: 'Amanda Garcia',
    service: 'Emergency Towing',
    rating: 4,
    date: '2023-08-30',
    content: 'Car broke down on the highway late at night. The tow truck arrived quickly and the driver was very helpful in getting me and my vehicle home safely.',
    location: 'Denver, CO',
    category: 'towing'
  },
]

export function TestimonialsContent() {
  const searchParams = useSearchParams()
  const categoryFilter = searchParams.get('category')
  const [filteredTestimonials, setFilteredTestimonials] = useState(testimonials)
  
  // Filter testimonials based on URL parameters
  useEffect(() => {
    if (categoryFilter) {
      setFilteredTestimonials(testimonials.filter(t => 
        t.category.toLowerCase() === categoryFilter.toLowerCase()
      ))
    } else {
      setFilteredTestimonials(testimonials)
    }
  }, [categoryFilter])
  
  // Get unique categories for filter buttons
  const categories = [...new Set(testimonials.map(t => t.category))]
  
  return (
    <div className="space-y-8">
      {/* Category filters */}
      <div className="flex flex-wrap gap-2 mb-8 justify-center">
        <Button 
          variant={!categoryFilter ? "default" : "outline"}
          className="mb-2"
          asChild
        >
          <a href="/testimonials">All Categories</a>
        </Button>
        
        {categories.map(category => (
          <Button 
            key={category}
            variant={categoryFilter === category ? "default" : "outline"}
            className="mb-2"
            asChild
          >
            <a href={`/testimonials?category=${category}`}>
              {category.charAt(0).toUpperCase() + category.slice(1)}
            </a>
          </Button>
        ))}
      </div>
      
      {/* Testimonials list */}
      {filteredTestimonials.length === 0 ? (
        <div className="text-center py-12">
          <h3 className="text-lg font-semibold mb-2">No testimonials found</h3>
          <p className="text-gray-500 mb-6">Try selecting a different category</p>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2">
          {filteredTestimonials.map((testimonial) => (
            <Card key={testimonial.id}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="font-semibold text-lg">{testimonial.name}</h3>
                    <p className="text-sm text-gray-500">{testimonial.location}</p>
                  </div>
                  <Badge>{testimonial.service}</Badge>
                </div>
                
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${
                        i < testimonial.rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
                      }`}
                    />
                  ))}
                  <span className="ml-2 text-sm text-gray-600">
                    {new Date(testimonial.date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </div>
                
                <p className="text-gray-700">{testimonial.content}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
