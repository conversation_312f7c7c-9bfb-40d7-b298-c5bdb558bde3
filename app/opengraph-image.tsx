import { ImageResponse } from 'next/og'

export const runtime = 'edge'

export const alt = 'Urgent Aid Locator - Find 24/7 Emergency Services'
export const size = {
  width: 1200,
  height: 630,
}
export const contentType = 'image/png'

export default async function Image() {
  return new ImageResponse(
    (
      <div
        style={{
          background: 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)',
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          fontFamily: 'system-ui, sans-serif',
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            textAlign: 'center',
            color: 'white',
            padding: '40px',
          }}
        >
          <h1
            style={{
              fontSize: '72px',
              fontWeight: 'bold',
              margin: '0 0 20px 0',
              lineHeight: '1.1',
            }}
          >
            Urgent Aid Locator
          </h1>
          <p
            style={{
              fontSize: '32px',
              margin: '0 0 40px 0',
              opacity: 0.9,
              maxWidth: '800px',
              lineHeight: '1.3',
            }}
          >
            Find 24/7 Emergency Services Near You
          </p>
          <div
            style={{
              display: 'flex',
              gap: '30px',
              fontSize: '24px',
              opacity: 0.8,
            }}
          >
            <span>🔧 Plumbers</span>
            <span>🔑 Locksmiths</span>
            <span>🚗 Towing</span>
            <span>⚡ Electricians</span>
            <span>🏥 Urgent Care</span>
          </div>
        </div>
      </div>
    ),
    {
      ...size,
    }
  )
}
