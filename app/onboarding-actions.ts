"use server"

import userPrisma from "@/lib/user-prisma"

// Check if user needs onboarding
export async function checkOnboardingStatus(userId: string) {
  try {
    const user = await userPrisma.user.findUnique({
      where: { id: userId },
      select: {
        hasCompletedOnboarding: true,
        onboardingStartedAt: true,
        onboardingCompletedAt: true,
        onboardingStep: true,
        role: true,
      },
    })

    if (!user) {
      return { needsOnboarding: false, error: "User not found" }
    }

    // Only business users (non-admin) need onboarding
    const isBusinessUser = user.role === "USER"
    const needsOnboarding = isBusinessUser && !user.hasCompletedOnboarding

    return {
      needsOnboarding,
      hasStarted: !!user.onboardingStartedAt,
      currentStep: user.onboardingStep,
      completedAt: user.onboardingCompletedAt,
      isBusinessUser,
    }
  } catch (error) {
    console.error("Error checking onboarding status:", error)
    return { needsOnboarding: false, error: "Failed to check onboarding status" }
  }
}

// Start onboarding process
export async function startOnboarding(userId: string, initialStep: string = "business-info") {
  try {
    await userPrisma.user.update({
      where: { id: userId },
      data: {
        onboardingStartedAt: new Date(),
        onboardingStep: initialStep,
      },
    })

    return { success: true }
  } catch (error) {
    console.error("Error starting onboarding:", error)
    return { success: false, error: "Failed to start onboarding" }
  }
}

// Update onboarding progress
export async function updateOnboardingStep(userId: string, step: string) {
  try {
    await userPrisma.user.update({
      where: { id: userId },
      data: {
        onboardingStep: step,
      },
    })

    return { success: true }
  } catch (error) {
    console.error("Error updating onboarding step:", error)
    return { success: false, error: "Failed to update onboarding step" }
  }
}

// Complete onboarding
export async function completeOnboarding(userId: string) {
  try {
    await userPrisma.user.update({
      where: { id: userId },
      data: {
        hasCompletedOnboarding: true,
        onboardingCompletedAt: new Date(),
        onboardingStep: null, // Clear the step since onboarding is complete
      },
    })

    return { success: true }
  } catch (error) {
    console.error("Error completing onboarding:", error)
    return { success: false, error: "Failed to complete onboarding" }
  }
}

// Check if user has an existing business registration
export async function hasExistingBusiness(userId: string) {
  try {
    const business = await userPrisma.registeredBusiness.findFirst({
      where: { userId },
    })

    return { hasExisting: !!business, business }
  } catch (error) {
    console.error("Error checking existing business:", error)
    return { hasExisting: false, error: "Failed to check existing business" }
  }
}

// Save onboarding progress (for resuming later)
export async function saveOnboardingProgress(
  userId: string,
  step: string,
  formData: any
) {
  try {
    // For now, we'll just update the step. In the future, we could store
    // partial form data in a separate table or JSON field
    await userPrisma.user.update({
      where: { id: userId },
      data: {
        onboardingStep: step,
      },
    })

    return { success: true }
  } catch (error) {
    console.error("Error saving onboarding progress:", error)
    return { success: false, error: "Failed to save progress" }
  }
}

// Reset onboarding (for testing or if user wants to start over)
export async function resetOnboarding(userId: string) {
  try {
    await userPrisma.user.update({
      where: { id: userId },
      data: {
        hasCompletedOnboarding: false,
        onboardingStartedAt: null,
        onboardingCompletedAt: null,
        onboardingStep: null,
      },
    })

    return { success: true }
  } catch (error) {
    console.error("Error resetting onboarding:", error)
    return { success: false, error: "Failed to reset onboarding" }
  }
}
