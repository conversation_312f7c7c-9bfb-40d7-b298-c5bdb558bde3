import { MetadataRoute } from 'next'
import { getAllCategories } from '@/lib/db-access'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://urgent-aid-locator.vercel.app'
  
  // Get all categories for dynamic routes
  const categories = await getAllCategories()
  
  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/search`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/categories`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/business/register`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    },
  ]

  // Category pages
  const categoryPages = categories.map((category) => ({
    url: `${baseUrl}/search?category=${encodeURIComponent(category)}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.7,
  }))

  // Popular emergency service combinations
  const emergencyServices = [
    'emergency plumber',
    'emergency locksmith', 
    '24 hour towing',
    'urgent care',
    'emergency veterinarian',
    'emergency electrician',
    'emergency hvac',
    'emergency dental'
  ]

  const emergencyPages = emergencyServices.map((service) => ({
    url: `${baseUrl}/search?q=${encodeURIComponent(service)}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }))

  return [...staticPages, ...categoryPages, ...emergencyPages]
}
