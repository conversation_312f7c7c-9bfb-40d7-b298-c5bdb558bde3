'use server'

import { hash } from "bcryptjs";
import userPrisma from "@/lib/user-prisma";
import { randomBytes } from "crypto";

// Register a new user
export async function registerUser(data: {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}) {
  try {
    // Check if user already exists
    const existingUser = await userPrisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      return { success: false, error: "Email already in use" };
    }

    // Hash the password
    const passwordHash = await hash(data.password, 10);
    
    // Create verification token
    const verificationToken = randomBytes(32).toString("hex");

    // Create the user
    const user = await userPrisma.user.create({
      data: {
        email: data.email,
        passwordHash,
        name: `${data.firstName} ${data.lastName}`,
        verificationToken,
        isEmailVerified: false, // Set to true for now since we're not implementing email verification yet
      },
    });

    // In a real app, you would send a verification email here
    // For now, we'll just return success

    return { success: true, userId: user.id };
  } catch (error) {
    console.error("Registration error:", error);
    return { success: false, error: "Failed to create account" };
  }
}

// Request password reset
export async function requestPasswordReset(email: string) {
  try {
    // Check if user exists
    const user = await userPrisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      // Don't reveal that the user doesn't exist for security reasons
      return { success: true };
    }

    // Generate reset token
    const resetToken = randomBytes(32).toString("hex");
    const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour from now

    // Update user with reset token
    await userPrisma.user.update({
      where: { id: user.id },
      data: {
        resetToken,
        resetTokenExpiry,
      },
    });

    // In a real app, you would send a password reset email here
    // For now, we'll just return success

    return { success: true };
  } catch (error) {
    console.error("Password reset request error:", error);
    return { success: false, error: "Failed to process request" };
  }
}

// Reset password with token
export async function resetPassword(data: {
  token: string;
  password: string;
}) {
  try {
    // Find user with this reset token and ensure it's not expired
    const user = await userPrisma.user.findFirst({
      where: {
        resetToken: data.token,
        resetTokenExpiry: {
          gt: new Date(),
        },
      },
    });

    if (!user) {
      return { success: false, error: "Invalid or expired reset token" };
    }

    // Hash the new password
    const passwordHash = await hash(data.password, 10);

    // Update user with new password and clear reset token
    await userPrisma.user.update({
      where: { id: user.id },
      data: {
        passwordHash,
        resetToken: null,
        resetTokenExpiry: null,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Password reset error:", error);
    return { success: false, error: "Failed to reset password" };
  }
}

// Register a new business
export async function registerBusiness(userId: string, data: {
  businessInfo: {
    name: string;
    description: string;
    yearEstablished: string;
    website: string;
    isEmergencyProvider: boolean;
  };
  categories: {
    selectedCategories: string[];
    otherCategory?: string;
  };
  hours: {
    schedule: Record<string, { open: string; close: string; closed: boolean }>;
  };
  contact: {
    phone: string;
    email: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
}) {
  try {
    // Format categories
    const categories = [...data.categories.selectedCategories];
    if (data.categories.otherCategory) {
      categories.push(data.categories.otherCategory);
    }
    
    // Create business profile ONLY in user database (persistent)
    // The main business database gets rebuilt weekly, so we don't store there
    const business = await userPrisma.registeredBusiness.create({
      data: {
        userId,
        businessName: data.businessInfo.name,
        description: data.businessInfo.description,
        phone: data.contact.phone,
        email: data.contact.email,
        website: data.businessInfo.website,
        address: data.contact.address,
        city: data.contact.city,
        state: data.contact.state,
        zipCode: data.contact.zipCode,
        categories: categories.join(", "),
        is24Hours: data.businessInfo.isEmergencyProvider,
        yearEstablished: data.businessInfo.yearEstablished,
        // Store hours would typically be stored in a separate table or as JSON
        hoursOfOperation: JSON.stringify(data.hours.schedule),
        status: "PENDING_REVIEW", // Require admin review before approval
        imageUrl: data.businessInfo.imageUrl, // Store uploaded image URL
        // No externalBusinessId since we're not creating in main DB
      },
    });

    return { success: true, businessId: business.id };
  } catch (error) {
    console.error("Business registration error:", error);
    return { success: false, error: "Failed to register business" };
  }
}

// Get business for a user
export async function getBusinessForUser(userId: string) {
  try {
    const business = await userPrisma.registeredBusiness.findFirst({
      where: { userId },
    });
    return business;
  } catch (error) {
    console.error("Error fetching business for user:", error);
    return null;
  }
}

// Get registered business by ID
export async function getRegisteredBusinessById(businessId: string) {
  try {
    const business = await userPrisma.registeredBusiness.findUnique({
      where: { id: businessId },
    });
    return business;
  } catch (error) {
    console.error("Error fetching registered business:", error);
    return null;
  }
}

// Update a registered business (only in persistent users database)
export async function updateBusiness(data: any) {
  try {
    // Update ONLY the registered business in the user database
    // Main database gets rebuilt weekly, so we don't update it
    await userPrisma.registeredBusiness.update({
      where: { id: data.id },
      data: {
        businessName: data.businessName,
        description: data.description,
        phone: data.phone,
        email: data.email,
        website: data.website,
        address: data.address,
        city: data.city,
        state: data.state,
        zipCode: data.zipCode,
        categories: data.categories,
        yearEstablished: data.yearEstablished,
        imageUrl: data.imageUrl, // Update image URL
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Business update error:", error);
    return { success: false, error: "Failed to update business" };
  }
}

// Approve a registered business and add it to the main database
export async function approveRegisteredBusiness(registeredBusinessId: string) {
  try {
    // Get the registered business
    const registeredBusiness = await userPrisma.registeredBusiness.findUnique({
      where: { id: registeredBusinessId },
    });

    if (!registeredBusiness) {
      return { success: false, error: "Registered business not found" };
    }

    // Import the main database prisma client
    const { prisma } = await import("@/lib/prisma");

    // Create a new business in the main database
    const newBusiness = await prisma.business.create({
      data: {
        id: `reg-${registeredBusiness.id}`, // Prefix to avoid ID conflicts
        name: registeredBusiness.businessName,
        categories: registeredBusiness.categories,
        phone: registeredBusiness.phone,
        address: registeredBusiness.address,
        city: registeredBusiness.city,
        term: registeredBusiness.categories.split(",")[0]?.trim() || "",
        url: registeredBusiness.website || "",
        rating: 0, // New business starts with no rating
        reviewCount: 0,
        isClosed: 0,
        latitude: 0, // Would need geocoding service to get coordinates
        longitude: 0,
        zipCode: registeredBusiness.zipCode,
        region: registeredBusiness.state,
        lastUpdated: new Date().toISOString(),
        price: null,
        imageUrl: null,
        // Add other fields as needed
      },
    });

    // Update the registered business with the external ID and approved status
    await userPrisma.registeredBusiness.update({
      where: { id: registeredBusinessId },
      data: {
        externalBusinessId: newBusiness.id,
        status: "APPROVED",
      },
    });

    return { success: true, businessId: newBusiness.id };
  } catch (error) {
    console.error("Business approval error:", error);
    return { success: false, error: "Failed to approve business" };
  }
}
