import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import Script from "next/script"
import "./globals.css"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/toaster"
import { SessionProvider } from "@/components/session-provider"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: {
    default: "Find 24/7 Emergency Services Near You | Urgent Aid Locator",
    template: "%s | Urgent Aid Locator"
  },
  description: "Search and discover nearby emergency plumbers, locksmiths, towing, urgent care clinics, veterinarians, and more. All available 24/7 when you need help most.",
  keywords: [
    "24/7 emergency services",
    "emergency plumber",
    "emergency locksmith",
    "urgent care",
    "emergency towing",
    "emergency veterinarian",
    "24 hour services",
    "emergency repairs",
    "urgent services near me",
    "after hours services"
  ],
  authors: [{ name: "Urgent Aid Locator" }],
  creator: "Urgent Aid Locator",
  publisher: "Urgent Aid Locator",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || 'https://urgent-aid-locator.vercel.app'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'Find 24/7 Emergency Services Near You | Urgent Aid Locator',
    description: 'Search and discover nearby emergency plumbers, locksmiths, towing, urgent care clinics, veterinarians, and more. All available 24/7 when you need help most.',
    siteName: 'Urgent Aid Locator',
    images: [
      {
        url: '/opengraph-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Urgent Aid Locator - Find 24/7 Emergency Services',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Find 24/7 Emergency Services Near You | Urgent Aid Locator',
    description: 'Search and discover nearby emergency plumbers, locksmiths, towing, urgent care clinics, veterinarians, and more. All available 24/7.',
    images: ['/opengraph-image.jpg'],
    creator: '@urgentaidlocator',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
}

const organizationSchema = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'Urgent Aid Locator',
  description: 'Directory platform for finding 24/7 emergency services including plumbers, locksmiths, towing, urgent care, and veterinarians.',
  url: process.env.NEXT_PUBLIC_BASE_URL || 'https://urgent-aid-locator.vercel.app',
  logo: {
    '@type': 'ImageObject',
    url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://urgent-aid-locator.vercel.app'}/logo.png`,
  },
  sameAs: [
    // Add social media URLs when available
  ],
  contactPoint: {
    '@type': 'ContactPoint',
    contactType: 'customer service',
    availableLanguage: 'English'
  }
}

const websiteSchema = {
  '@context': 'https://schema.org',
  '@type': 'WebSite',
  name: 'Urgent Aid Locator',
  url: process.env.NEXT_PUBLIC_BASE_URL || 'https://urgent-aid-locator.vercel.app',
  description: 'Find 24/7 emergency services near you including plumbers, locksmiths, towing, urgent care, and veterinarians.',
  potentialAction: {
    '@type': 'SearchAction',
    target: {
      '@type': 'EntryPoint',
      urlTemplate: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://urgent-aid-locator.vercel.app'}/search?q={search_term_string}`
    },
    'query-input': 'required name=search_term_string'
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <Script
          id="organization-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationSchema),
          }}
        />
        <Script
          id="website-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(websiteSchema),
          }}
        />
      </head>
      <body className={inter.className} suppressHydrationWarning={true}>
        <SessionProvider>
          <ThemeProvider attribute="class" defaultTheme="light" enableSystem={false} disableTransitionOnChange>
            <Header />
            {children}
            <Footer />
            <Toaster />
          </ThemeProvider>
        </SessionProvider>
      </body>
    </html>
  )
}
