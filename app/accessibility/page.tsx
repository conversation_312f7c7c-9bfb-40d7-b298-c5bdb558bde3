export default function AccessibilityPage() {
  return (
    <main className="container mx-auto max-w-4xl px-4 py-12">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">Accessibility Statement</h1>
        <p className="text-gray-600">Last Updated: June 1, 2023</p>
      </div>

      <div className="prose max-w-none">
        <p>
          EmergencyFind is committed to ensuring digital accessibility for people with disabilities. We are continually
          improving the user experience for everyone and applying the relevant accessibility standards.
        </p>

        <h2>Our Commitment to Accessibility</h2>
        <p>
          We strive to ensure that our website and mobile applications are accessible to all users, including those with
          disabilities. Our goal is to meet Level AA standards of the Web Content Accessibility Guidelines (WCAG) 2.1.
        </p>

        <h2>Measures We Take</h2>
        <p>We take the following measures to ensure accessibility:</p>
        <ul>
          <li>Include accessibility as part of our mission statement</li>
          <li>Integrate accessibility into our design and development processes</li>
          <li>Conduct regular accessibility audits and testing</li>
          <li>Provide accessibility training for our staff</li>
          <li>Engage with users with disabilities for feedback</li>
        </ul>

        <h2>Accessibility Features</h2>
        <p>Our website includes the following accessibility features:</p>
        <ul>
          <li>Semantic HTML to ensure proper structure and navigation</li>
          <li>ARIA landmarks to identify regions of the page</li>
          <li>Alt text for all informative images</li>
          <li>Sufficient color contrast for text and important graphics</li>
          <li>Keyboard accessibility for all interactive elements</li>
          <li>Resizable text without loss of functionality</li>
          <li>Clear focus indicators for keyboard navigation</li>
          <li>Skip navigation links</li>
          <li>Descriptive link text</li>
          <li>Form labels and error messages</li>
        </ul>

        <h2>Compatibility with Assistive Technologies</h2>
        <p>Our website is designed to be compatible with a variety of assistive technologies, including:</p>
        <ul>
          <li>Screen readers (such as JAWS, NVDA, VoiceOver, and TalkBack)</li>
          <li>Screen magnification software</li>
          <li>Speech recognition software</li>
          <li>Keyboard-only navigation</li>
        </ul>

        <h2>Known Limitations</h2>
        <p>While we strive to ensure that our website is accessible to all users, there may be some limitations:</p>
        <ul>
          <li>Some older content may not fully meet our accessibility standards</li>
          <li>Third-party content or applications on our website may not be fully accessible</li>
          <li>We are continuously working to address these limitations and improve accessibility</li>
        </ul>

        <h2>Feedback and Contact Information</h2>
        <p>
          We welcome your feedback on the accessibility of EmergencyFind. Please let us know if you encounter any
          accessibility barriers:
        </p>
        <ul>
          <li>
            Email:{" "}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>
          </li>
          <li>
            Phone:{" "}
            <a href="tel:+***********" className="text-blue-600 hover:underline">
              (*************
            </a>
          </li>
          <li>
            Feedback form:{" "}
            <a href="/contact" className="text-blue-600 hover:underline">
              Contact Us
            </a>
          </li>
        </ul>
        <p>
          We try to respond to feedback within 2 business days and are committed to addressing accessibility issues in a
          timely manner.
        </p>

        <h2>Assessment and Compliance</h2>
        <p>
          Our website's accessibility is regularly evaluated and tested by internal teams and occasionally by external
          consultants. We test our website using a combination of automated tools and manual testing with assistive
          technologies.
        </p>

        <h2>Additional Resources</h2>
        <p>For more information about web accessibility, please visit the following resources:</p>
        <ul>
          <li>
            <a
              href="https://www.w3.org/WAI/standards-guidelines/wcag/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              Web Content Accessibility Guidelines (WCAG)
            </a>
          </li>
          <li>
            <a
              href="https://www.w3.org/WAI/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              W3C Web Accessibility Initiative (WAI)
            </a>
          </li>
          <li>
            <a
              href="https://www.ada.gov/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              Americans with Disabilities Act (ADA)
            </a>
          </li>
        </ul>

        <h2>Continuous Improvement</h2>
        <p>
          We are committed to continuously improving the accessibility of our website and services. We regularly review
          our website and content to identify and address any accessibility issues, and we incorporate accessibility
          into our ongoing development and design processes.
        </p>
      </div>
    </main>
  )
}
