import { NextRequest, NextResponse } from 'next/server';
import { getPopularBusinessesAction } from '@/app/actions';
import { Logger } from '@logtail/next';
import { PopularService } from '@/lib/search-tracking-service';

export async function GET(request: NextRequest) {
  const log = new Logger();

  try {
    log.info('Popular services API - request received');

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '3', 10);

    console.log('Popular services API - fetching data', { limit });
    log.info('Popular services API - fetching data', { limit });

    // Fetch popular businesses based on clicks
    const popularBusinesses = await getPopularBusinessesAction(limit);

    // Transform the data to match the PopularService type expected by the frontend
    const popularServices: PopularService[] = popularBusinesses.map(business => ({
      searchTerm: business.businessName,
      searchCount: business.viewCount,
      lastSearchedAt: business.lastViewedAt,
      sampleBusinesses: business.business ? [business.business] : [],
    }));

    console.log('Popular services API - data fetched', {
      count: popularServices.length,
      sample: popularServices.length > 0 ? popularServices[0].searchTerm : 'none',
    });
    log.info('Popular services API - data fetched', {
      count: popularServices.length,
      sample: popularServices.length > 0 ? popularServices[0].searchTerm : 'none',
    });

    await log.flush();

    return NextResponse.json(popularServices, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Surrogate-Control': 'no-store',
      },
    });
  } catch (error) {
    console.error('Popular services API - error:', error);
    log.error('Popular services API - error', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    await log.flush();

    return NextResponse.json([], {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Surrogate-Control': 'no-store',
      },
    });
  }
}
