import { NextRequest, NextResponse } from 'next/server'
import userPrisma from '@/lib/user-prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const businessId = searchParams.get('businessId')
    const userId = searchParams.get('userId')

    if (!businessId || !userId) {
      return NextResponse.json({ isOwner: false })
    }

    // Check if this user owns this registered business
    const registeredBusiness = await userPrisma.registeredBusiness.findFirst({
      where: {
        id: businessId,
        userId,
      },
    })

    return NextResponse.json({ isOwner: !!registeredBusiness })
  } catch (error) {
    console.error('Error checking registered business ownership:', error)
    return NextResponse.json({ isOwner: false })
  }
}
