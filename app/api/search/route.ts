import { NextRequest, NextResponse } from 'next/server'
import { searchBusinessesAction } from '@/app/actions'
import { withBetterStack } from '@logtail/next'

async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)

  // Extract basic search parameters
  const query = searchParams.get('q') || undefined
  const location = searchParams.get('location') || undefined
  const category = searchParams.get('category') || undefined
  const page = searchParams.get('page') ? parseInt(searchParams.get('page') as string) : 1
  const pageSize = searchParams.get('pageSize') ? parseInt(searchParams.get('pageSize') as string) : 10

  // Extract filter parameters
  const distance = searchParams.get('distance') ? parseInt(searchParams.get('distance') as string) : undefined
  const open24 = searchParams.get('open24') === 'true'
  const verified = searchParams.get('verified') === 'true'
  const rating = searchParams.get('rating') ? parseInt(searchParams.get('rating') as string) : undefined
  const sortBy = searchParams.get('sortBy') as "rating" | "distance" | undefined

  try {
    console.log("API Search request with parameters:");
    request.log.info("API Search request with parameters:", {
      query: query || '',
      location: location || '',
      category: category || '',
      page,
      pageSize,
      distance: distance !== undefined ? distance + ' miles' : 'Not specified',
      open24: open24 ? 'Yes' : 'No',
      verified: verified ? 'Yes' : 'No',
      rating: rating !== undefined ? rating + '+ stars' : 'Any',
      sortBy: sortBy || 'default'
    });
    console.log(`- Query: "${query || ''}"`);
    console.log(`- Location: "${location || ''}"`);
    console.log(`- Category: "${category || ''}"`);
    console.log(`- Page: ${page}`);
    console.log(`- Page Size: ${pageSize}`);
    console.log(`- Distance: ${distance !== undefined ? distance + ' miles' : 'Not specified'}`);
    console.log(`- Open 24 Hours: ${open24 ? 'Yes' : 'No'}`);
    console.log(`- Verified: ${verified ? 'Yes' : 'No'}`);
    console.log(`- Rating: ${rating !== undefined ? rating + '+ stars' : 'Any'}`);
    console.log(`- Sort By: ${sortBy || 'default'}`);

    const result = await searchBusinessesAction({
      query,
      location,
      category,
      page,
      pageSize,
      distance,
      open24,
      verified,
      rating,
      sortBy,
    })

    console.log(`API Search returned ${result.businesses.length} results out of ${result.totalCount} total`);
    request.log.info(`API Search returned results`, {
      resultsCount: result.businesses.length,
      totalCount: result.totalCount
    });

    return NextResponse.json(result)
  } catch (error) {
    console.error('Search API error:', error)
    request.log.error('Search API error:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    return NextResponse.json({ error: 'Failed to fetch search results' }, { status: 500 })
  }
}

export const GET_wrapped = withBetterStack(GET);
export { GET_wrapped as GET };
