import { NextRequest, NextResponse } from 'next/server'
import userPrisma from '@/lib/user-prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const businessId = searchParams.get('businessId')
    const userId = searchParams.get('userId')

    if (!businessId || !userId) {
      return NextResponse.json({ isOwner: false })
    }

    // Check if this user has a registered business that links to this business ID
    const registeredBusiness = await userPrisma.registeredBusiness.findFirst({
      where: {
        userId,
        externalBusinessId: businessId,
      },
    })

    if (registeredBusiness) {
      return NextResponse.json({ 
        isOwner: true, 
        registeredBusinessId: registeredBusiness.id 
      })
    }

    return NextResponse.json({ isOwner: false })
  } catch (error) {
    console.error('Error checking business ownership:', error)
    return NextResponse.json({ isOwner: false })
  }
}
