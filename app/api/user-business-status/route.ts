import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth-config'
import { getBusinessForUser } from '@/app/auth-actions'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ hasBusiness: false, error: 'Not authenticated' })
    }

    const business = await getBusinessForUser(session.user.id)
    
    return NextResponse.json({ 
      hasBusiness: !!business,
      businessId: business?.id || null
    })
  } catch (error) {
    console.error('Error checking user business status:', error)
    return NextResponse.json({ hasBusiness: false, error: 'Failed to check business status' })
  }
}
