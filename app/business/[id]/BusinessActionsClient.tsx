"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Phone, MapPin, ThumbsUp } from "lucide-react"

interface BusinessActionsClientProps {
  phone: string
  latitude: number
  longitude: number
}

export default function BusinessActionsClient({ phone, latitude, longitude }: BusinessActionsClientProps) {
  return (
    <div className="flex flex-wrap gap-3">
      <Button size="lg" className="gap-2">
        <Phone className="h-5 w-5" />
        Call Now
      </Button>
      <Button
        size="lg"
        variant="outline"
        className="gap-2"
        onClick={() => {
          window.open(`https://maps.google.com/?q=${latitude},${longitude}`, "_blank")
        }}
      >
        <MapPin className="h-5 w-5" />
        Get Directions
      </Button>
      <Button size="lg" variant="outline" className="gap-2">
        <ThumbsUp className="h-5 w-5" />
        Save
      </Button>
    </div>
  )
}
