import React from "react"
import { use } from "react"
import Image from "next/image"
import Link from "next/link"
import { redirect } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { BusinessReviews } from "@/components/business-reviews"
import { BusinessServices } from "@/components/business-services"
import { SimilarBusinesses } from "@/components/similar-businesses"
import { Clock, Globe, MapPin, Phone, Star, ThumbsUp, Edit } from "lucide-react"
import { getBusinessByIdEnhanced } from "@/lib/enhanced-search-service"
import { recordBusinessViewAction } from "@/app/actions"
import BusinessActionsClient from "./BusinessActionsClient"
import SimilarBusinessesWrapper from "./SimilarBusinessesWrapper"
import BackButton from "./BackButton"
import MapTabContent from "./MapTabContent"


export default function BusinessPage({ params }: { params: Promise<{ id: string }> }) {
  const unwrappedParams = use(params)

  // If this is a registered business ID, redirect to the registered business page
  if (unwrappedParams.id.startsWith('reg-')) {
    const registeredId = unwrappedParams.id.replace('reg-', '')
    redirect(`/business/registered/${registeredId}`)
  }

  const business = use(getBusinessByIdEnhanced(unwrappedParams.id))

  if (!business) {
    return (
      <div className="container mx-auto max-w-6xl px-4 py-12 text-center">
        <h1 className="text-2xl font-bold mb-4">Business not found</h1>
        <p className="mb-6">The business you're looking for doesn't exist or has been removed.</p>
        <Link href="/">
          <Button>Return to Homepage</Button>
        </Link>
      </div>
    )
  }

  // Record business view (fire and forget - don't wait for it)
  try {
    recordBusinessViewAction(business.id, business.name).catch(error => {
      console.error('Failed to record business view:', error);
    });
  } catch (error) {
    console.error('Error setting up business view tracking:', error);
  }

  return (
  
    <main>
      {/* Hero Section */}
      <div className="bg-white border-b">
        <div className="container mx-auto max-w-6xl px-4 py-6">
          {/* Back Button */}
          <div className="mb-4">
            <BackButton />
          </div>
          
          <div className="flex flex-col md:flex-row gap-6 items-start">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                {business.categories.map((category, index) => (
                  <Badge key={index} variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">
                    {category}
                  </Badge>
                ))}
                {business.is24Hours && (
                  <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">
                    Open 24/7
                  </Badge>
                )}
              </div>
              <h1 className="text-3xl font-bold mb-2">{business.name}</h1>
              <div className="flex items-center gap-1 mb-4">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${i < business.rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}`}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-600">
                  {business.rating} ({business.reviewCount} reviews)
                </span>
              </div>
              <p className="text-gray-600 mb-6">
                {business.searchTerm} service provider in {business.city}
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="flex items-center gap-2">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Phone className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Phone</div>
                    <a href={`tel:${business.phone}`} className="font-medium text-blue-600">
                      {business.phone}
                    </a>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <MapPin className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Address</div>
                    <address className="not-italic font-medium">{business.address}</address>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Globe className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Website</div>
                    <a
                      href={business.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-medium text-blue-600"
                    >
                      Visit Website
                    </a>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Clock className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Hours</div>
                    <div className="font-medium">{business.is24Hours ? "Open 24/7" : "Call for hours"}</div>
                  </div>
                </div>
              </div>
              <BusinessActionsClient phone={business.phone} latitude={business.latitude} longitude={business.longitude} />
            </div>
            <div className="w-full md:w-1/3">
              <Card>
                <CardContent className="p-4">
                  <div className="aspect-video relative rounded-md overflow-hidden mb-4">
                    <Image
                      src={business.imageUrl || "/placeholder.svg?height=200&width=400"}
                      alt={business.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="bg-green-50 border border-green-100 rounded-md p-3 mb-4">
                    <h3 className="font-semibold text-green-800 flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      Availability
                    </h3>
                    <p className="text-green-700 text-sm">
                      {business.is24Hours ? "Available 24/7 for emergency services" : "Call for availability"}
                    </p>
                  </div>
                  {business.price && (
                    <div className="text-sm text-gray-500">
                      <p>Price range: {business.price}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs Section */}
      <div className="container mx-auto max-w-6xl px-4 py-6">
        <Tabs defaultValue="services">
          <TabsList className="mb-6">
            <TabsTrigger value="services">Services</TabsTrigger>
            <TabsTrigger value="reviews">Reviews</TabsTrigger>
            <TabsTrigger value="map">Map & Service Area</TabsTrigger>
          </TabsList>
          <TabsContent value="services">
            <BusinessServices businessId={unwrappedParams.id} searchTerm={business.searchTerm} />
          </TabsContent>
          <TabsContent value="reviews">
            <BusinessReviews businessId={unwrappedParams.id} rating={business.rating} reviewCount={business.reviewCount} />
          </TabsContent>
          <TabsContent value="map">
            <MapTabContent 
              businessName={business.name}
              latitude={business.latitude}
              longitude={business.longitude}
              address={business.address || ""}
              city={business.city || ""}
              searchTerm={business.searchTerm}
            />
          </TabsContent>
        </Tabs>
      </div>

      {/* Similar Businesses */}
   
      <div className="container mx-auto max-w-6xl px-4 py-6">
 <SimilarBusinessesWrapper categories={business.categories} currentBusinessId={unwrappedParams.id} />
       
      </div>

    </main>
  )
}
