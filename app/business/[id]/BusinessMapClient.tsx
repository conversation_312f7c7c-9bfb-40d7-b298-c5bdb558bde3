'use client'

import { useEffect, useRef } from 'react'
import L from 'leaflet'

interface BusinessMapClientProps {
  latitude: number
  longitude: number
  businessName: string
  address: string
  city: string
}

export default function BusinessMapClient({ latitude, longitude, businessName, address, city }: BusinessMapClientProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<L.Map | null>(null)

  useEffect(() => {
    // Skip if no coordinates or map already initialized
    if (!latitude || !longitude || !mapRef.current || mapInstanceRef.current) return

    // Fix for Leaflet icon issues in Next.js
    if (typeof window !== 'undefined') {
      // Only initialize map on client-side
      import('leaflet').then((L) => {
        // Fix icon paths
        delete (L.Icon.Default.prototype as any)._getIconUrl
        L.Icon.Default.mergeOptions({
          iconRetinaUrl: '/leaflet/marker-icon-2x.png',
          iconUrl: '/leaflet/marker-icon.png',
          shadowUrl: '/leaflet/marker-shadow.png',
        })

        // Create map
        const map = L.map(mapRef.current).setView([latitude, longitude], 14)
        mapInstanceRef.current = map

        // Add tile layer (OpenStreetMap)
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          maxZoom: 19,
        }).addTo(map)

        // Add marker for business location
        const marker = L.marker([latitude, longitude]).addTo(map)
        marker.bindPopup(`
          <strong>${businessName}</strong><br>
          ${address}<br>
          ${city}
        `).openPopup()

        // Add service area circle (approximate 5 mile radius)
        const serviceAreaRadius = 5 * 1609.34 // 5 miles in meters
        L.circle([latitude, longitude], {
          color: 'blue',
          fillColor: '#3b82f6',
          fillOpacity: 0.1,
          radius: serviceAreaRadius,
        }).addTo(map)
      })
    }

    // Cleanup function
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
      }
    }
  }, [latitude, longitude, businessName, address, city])

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.invalidateSize()
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return <div ref={mapRef} className="h-[400px] w-full rounded-md" />
}
