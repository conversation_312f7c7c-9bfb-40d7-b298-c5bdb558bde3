'use client'

import { useSession } from "next-auth/react"
import { useEffect, useState } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Edit } from "lucide-react"

interface BusinessOwnerActionsProps {
  businessId: string
}

export default function BusinessOwnerActions({ businessId }: BusinessOwnerActionsProps) {
  const { data: session } = useSession()
  const [isOwner, setIsOwner] = useState(false)
  const [registeredBusinessId, setRegisteredBusinessId] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function checkOwnership() {
      if (!session?.user?.id) {
        setIsLoading(false)
        return
      }

      try {
        // Check if this user owns a registered business that links to this business
        const response = await fetch(`/api/business-ownership?businessId=${businessId}&userId=${session.user.id}`)
        const data = await response.json()
        
        if (data.isOwner) {
          setIsOwner(true)
          setRegisteredBusinessId(data.registeredBusinessId)
        }
      } catch (error) {
        console.error('Error checking business ownership:', error)
      } finally {
        setIsLoading(false)
      }
    }

    checkOwnership()
  }, [session, businessId])

  if (isLoading || !isOwner) {
    return null
  }

  return (
    <div className="mb-4">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-blue-900">This is your business listing</h3>
            <p className="text-blue-700 text-sm">You can edit your business information and manage your listing.</p>
          </div>
          <Link href={`/business/dashboard/edit?id=${registeredBusinessId}`}>
            <Button className="gap-2">
              <Edit className="h-4 w-4" />
              Edit Listing
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
