import React from "react"
import { use } from "react"
import { SimilarBusinesses } from "@/components/similar-businesses"
import { getSimilarBusinesses, type Business } from "@/lib/data"

interface SimilarBusinessesWrapperProps {
  categories: string[]
  currentBusinessId: string
}

export default function SimilarBusinessesWrapper({ categories, currentBusinessId }: SimilarBusinessesWrapperProps) {
  const similarBusinesses: Business[] = use(getSimilarBusinesses(categories, currentBusinessId))

  return <SimilarBusinesses businesses={similarBusinesses} currentBusinessId={currentBusinessId} />
}
