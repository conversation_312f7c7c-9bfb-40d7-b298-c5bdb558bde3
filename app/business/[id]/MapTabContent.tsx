'use client'

import { useEffect } from 'react'
import dynamic from "next/dynamic"
import Head from 'next/head'

// Dynamically import the map component with no SSR
const BusinessMapClient = dynamic(() => import("./BusinessMapClient"), { ssr: false })

interface MapTabContentProps {
  businessName: string
  latitude: number | null
  longitude: number | null
  address: string
  city: string
  searchTerm: string
}

export default function MapTabContent({ 
  businessName, 
  latitude, 
  longitude, 
  address, 
  city,
  searchTerm 
}: MapTabContentProps) {
  // Add Leaflet CSS via a link tag
  useEffect(() => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
    link.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';
    link.crossOrigin = '';
    
    document.head.appendChild(link);
    
    return () => {
      document.head.removeChild(link);
    };
  }, []);

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <div className="aspect-[16/9] relative rounded-md overflow-hidden mb-4">
        {latitude && longitude ? (
          <BusinessMapClient 
            latitude={latitude} 
            longitude={longitude}
            businessName={businessName}
            address={address}
            city={city}
          />
        ) : (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <p className="text-gray-500">Map location not available</p>
          </div>
        )}
      </div>
      <h3 className="font-bold text-lg mb-2">Service Area</h3>
      <p className="text-gray-600 mb-4">
        {businessName} serves the {city} area with {searchTerm.toLowerCase()} services.
      </p>
    </div>
  )
}
