import React from "react"
import { use } from "react"
import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Clock, Globe, MapPin, Phone, Star, Edit } from "lucide-react"
import { getRegisteredBusinessById } from "@/app/auth-actions"
import RegisteredBusinessOwnerActions from "./RegisteredBusinessOwnerActions"
import BackButton from "../../[id]/BackButton"

export default function RegisteredBusinessPage({ params }: { params: Promise<{ id: string }> }) {
  const unwrappedParams = use(params)
  const business = use(getRegisteredBusinessById(unwrappedParams.id))

  if (!business) {
    return (
      <div className="container mx-auto max-w-6xl px-4 py-12 text-center">
        <h1 className="text-2xl font-bold mb-4">Business not found</h1>
        <p className="mb-6">The business you're looking for doesn't exist or has been removed.</p>
        <Link href="/">
          <Button>Return to Homepage</Button>
        </Link>
      </div>
    )
  }

  // Parse hours if available
  let parsedHours = null
  try {
    if (business.hoursOfOperation) {
      parsedHours = JSON.parse(business.hoursOfOperation)
    }
  } catch (error) {
    console.error('Error parsing hours:', error)
  }

  return (
    <main>
      {/* Hero Section */}
      <div className="bg-white border-b">
        <div className="container mx-auto max-w-6xl px-4 py-6">
          {/* Back Button */}
          <div className="mb-4">
            <BackButton />
          </div>
          
          {/* Business Owner Actions */}
          <RegisteredBusinessOwnerActions businessId={unwrappedParams.id} />

          <div className="flex flex-col md:flex-row gap-6">
            <div className="w-full md:w-2/3">
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h1 className="text-3xl font-bold">{business.businessName}</h1>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      Registered Business
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-4 text-gray-600 mb-4">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      <span>{business.address}, {business.city}, {business.state} {business.zipCode}</span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {business.categories.split(',').map((category, index) => (
                      <Badge key={index} variant="outline">
                        {category.trim()}
                      </Badge>
                    ))}
                  </div>

                  {business.description && (
                    <p className="text-gray-700 mb-4">{business.description}</p>
                  )}

                  <div className="flex flex-col sm:flex-row gap-3">
                    {business.phone && (
                      <Button className="flex-1 gap-2">
                        <Phone className="h-4 w-4" />
                        Call {business.phone}
                      </Button>
                    )}
                    {business.website && (
                      <Button variant="outline" className="flex-1 gap-2" asChild>
                        <Link href={business.website} target="_blank">
                          <Globe className="h-4 w-4" />
                          Visit Website
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="w-full md:w-1/3">
              <Card>
                <CardContent className="p-4">
                  <div className="aspect-video relative rounded-md overflow-hidden mb-4 bg-gray-100">
                    {business.imageUrl ? (
                      <Image
                        src={business.imageUrl}
                        alt={business.businessName}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full text-gray-500">
                        <span>No image available</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="bg-green-50 border border-green-100 rounded-md p-3 mb-4">
                    <h3 className="font-semibold text-green-800 flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      Availability
                    </h3>
                    <p className="text-green-700 text-sm">
                      {business.is24Hours ? "Available 24/7 for emergency services" : "Call for availability"}
                    </p>
                  </div>

                  {business.yearEstablished && (
                    <div className="text-sm text-gray-600 mb-2">
                      <strong>Established:</strong> {business.yearEstablished}
                    </div>
                  )}

                  <div className="text-sm text-gray-600">
                    <strong>Business Type:</strong> Registered Business
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs Section */}
      <div className="container mx-auto max-w-6xl px-4 py-6">
        <Tabs defaultValue="info">
          <TabsList className="mb-6">
            <TabsTrigger value="info">Business Info</TabsTrigger>
            <TabsTrigger value="hours">Hours</TabsTrigger>
            <TabsTrigger value="contact">Contact</TabsTrigger>
          </TabsList>
          
          <TabsContent value="info">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4">About This Business</h3>
                {business.description ? (
                  <p className="text-gray-700">{business.description}</p>
                ) : (
                  <p className="text-gray-500 italic">No description available.</p>
                )}
                
                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Categories</h4>
                    <div className="flex flex-wrap gap-2">
                      {business.categories.split(',').map((category, index) => (
                        <Badge key={index} variant="outline">
                          {category.trim()}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  {business.yearEstablished && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Year Established</h4>
                      <p className="text-gray-700">{business.yearEstablished}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="hours">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4">Business Hours</h3>
                {business.is24Hours ? (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <p className="text-green-800 font-medium">Available 24/7</p>
                    <p className="text-green-700 text-sm">This business provides emergency services around the clock.</p>
                  </div>
                ) : parsedHours ? (
                  <div className="space-y-2">
                    {Object.entries(parsedHours).map(([day, hours]: [string, any]) => (
                      <div key={day} className="flex justify-between py-2 border-b border-gray-100">
                        <span className="font-medium capitalize">{day}</span>
                        <span className="text-gray-600">
                          {hours.isClosed ? 'Closed' : `${hours.open} - ${hours.close}`}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 italic">Hours not specified. Please call for availability.</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="contact">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4">Contact Information</h3>
                <div className="space-y-4">
                  {business.phone && (
                    <div className="flex items-center gap-3">
                      <Phone className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="font-medium">Phone</p>
                        <p className="text-gray-600">{business.phone}</p>
                      </div>
                    </div>
                  )}
                  
                  {business.email && (
                    <div className="flex items-center gap-3">
                      <Globe className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="font-medium">Email</p>
                        <p className="text-gray-600">{business.email}</p>
                      </div>
                    </div>
                  )}
                  
                  {business.website && (
                    <div className="flex items-center gap-3">
                      <Globe className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="font-medium">Website</p>
                        <Link href={business.website} target="_blank" className="text-blue-600 hover:underline">
                          {business.website}
                        </Link>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex items-start gap-3">
                    <MapPin className="h-5 w-5 text-gray-400 mt-1" />
                    <div>
                      <p className="font-medium">Address</p>
                      <p className="text-gray-600">
                        {business.address}<br />
                        {business.city}, {business.state} {business.zipCode}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </main>
  )
}
