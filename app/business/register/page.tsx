"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { BusinessRegistrationForm } from "@/components/business-registration-form"
import { BusinessCategoriesForm } from "@/components/business-categories-form"
import { BusinessHoursForm } from "@/components/business-hours-form"
import { BusinessContactForm } from "@/components/business-contact-form"
import { BusinessReviewForm } from "@/components/business-review-form"
import { AlertCircle, CheckCircle2, LockIcon } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { registerBusiness } from "@/app/auth-actions"

export default function BusinessRegisterPage() {
  const router = useRouter()
  const { data: session, status } = useSession()
  const [activeTab, setActiveTab] = useState("business-info")
  const [formData, setFormData] = useState({
    businessInfo: {
      name: "",
      description: "",
      yearEstablished: "",
      website: "",
      isEmergencyProvider: true,
      imageUrl: null as string | null,
    },
    categories: {
      primaryCategory: "",
      additionalCategories: [] as string[],
      searchTerms: [] as string[],
    },
    hours: {
      is24Hours: false,
      regularHours: {
        monday: { open: "09:00", close: "17:00", isClosed: false },
        tuesday: { open: "09:00", close: "17:00", isClosed: false },
        wednesday: { open: "09:00", close: "17:00", isClosed: false },
        thursday: { open: "09:00", close: "17:00", isClosed: false },
        friday: { open: "09:00", close: "17:00", isClosed: false },
        saturday: { open: "10:00", close: "15:00", isClosed: false },
        sunday: { open: "10:00", close: "15:00", isClosed: true },
      },
      holidayHours: "",
      emergencyAfterHours: false, // New field for emergency after-hours availability
    },
    contact: {
      phone: "",
      email: "",
      address: "",
      city: "",
      state: "",
      zipCode: "",
      serviceRadius: "10",
    },
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [error, setError] = useState("")

  const updateFormData = (section: string, data: any) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        ...data,
      },
    }))
  }

  const handleNext = () => {
    if (activeTab === "business-info") setActiveTab("categories")
    else if (activeTab === "categories") setActiveTab("hours")
    else if (activeTab === "hours") setActiveTab("contact")
    else if (activeTab === "contact") setActiveTab("review")
  }

  const handleBack = () => {
    if (activeTab === "categories") setActiveTab("business-info")
    else if (activeTab === "hours") setActiveTab("categories")
    else if (activeTab === "contact") setActiveTab("hours")
    else if (activeTab === "review") setActiveTab("contact")
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    setError("")

    try {
      if (!session?.user?.id) {
        setError("You must be logged in to register a business")
        return
      }

      // Call the server action to register the business
      const result = await registerBusiness(session.user.id, {
        businessInfo: formData.businessInfo,
        categories: {
          selectedCategories: [
            formData.categories.primaryCategory,
            ...formData.categories.additionalCategories
          ],
          otherCategory: ""
        },
        hours: {
          schedule: formData.hours.regularHours
        },
        contact: formData.contact
      })

      if (!result.success) {
        setError(result.error || "An error occurred while registering your business. Please try again.")
        return
      }

      setIsSuccess(true)

      // Redirect to the registered business profile page after a delay
      setTimeout(() => {
        if (result.businessId) {
          router.push(`/business/registered/${result.businessId}`)
        } else {
          router.push("/business/dashboard")
        }
      }, 2000)
    } catch (err) {
      setError("An error occurred while registering your business. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  // If not authenticated, show login prompt
  if (status === "unauthenticated") {
    return (
      <div className="container max-w-4xl mx-auto py-12 px-4">
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-3xl font-bold">List Your Business</CardTitle>
            <CardDescription className="text-lg">
              Join our directory of emergency service providers and connect with customers when they need you most
            </CardDescription>
          </CardHeader>
          <CardContent className="py-6 text-center">
            <div className="flex flex-col items-center justify-center py-12">
              <LockIcon className="h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-xl font-semibold mb-2">Authentication Required</h3>
              <p className="text-gray-600 mb-6 max-w-md">
                You need to be logged in to register your business. Please log in or create an account to continue.
              </p>
              <div className="flex gap-4">
                <Button onClick={() => router.push("/login?redirect=/business/register")}>
                  Log In
                </Button>
                <Button variant="outline" onClick={() => router.push("/signup")}>
                  Create Account
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container max-w-4xl mx-auto py-12 px-4">
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-bold">List Your Business</CardTitle>
          <CardDescription className="text-lg">
            Join our directory of emergency service providers and connect with customers when they need you most
          </CardDescription>
        </CardHeader>
        <CardContent className="py-6">
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isSuccess ? (
            <Alert className="mb-6 bg-blue-50 border-blue-200">
              <CheckCircle2 className="h-4 w-4 text-blue-600" />
              <AlertTitle className="text-blue-800">Registration Submitted!</AlertTitle>
              <AlertDescription className="text-blue-700">
                Your business registration has been submitted successfully and is now pending admin review.
                You'll be notified once your listing is approved and appears in search results.
                This typically takes 1-2 business days.
              </AlertDescription>
            </Alert>
          ) : (
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="business-info">Business Info</TabsTrigger>
                <TabsTrigger value="categories">Categories</TabsTrigger>
                <TabsTrigger value="hours">Hours</TabsTrigger>
                <TabsTrigger value="contact">Contact</TabsTrigger>
                <TabsTrigger value="review">Review</TabsTrigger>
              </TabsList>

              <TabsContent value="business-info">
                <BusinessRegistrationForm
                  data={formData.businessInfo}
                  updateData={(data) => updateFormData("businessInfo", data)}
                  onNext={handleNext}
                />
              </TabsContent>

              <TabsContent value="categories">
                <BusinessCategoriesForm
                  data={formData.categories}
                  updateData={(data) => updateFormData("categories", data)}
                  onNext={handleNext}
                  onBack={handleBack}
                />
              </TabsContent>

              <TabsContent value="hours">
                <BusinessHoursForm
                  data={formData.hours}
                  updateData={(data) => updateFormData("hours", data)}
                  onNext={handleNext}
                  onBack={handleBack}
                />
              </TabsContent>

              <TabsContent value="contact">
                <BusinessContactForm
                  data={formData.contact}
                  updateData={(data) => updateFormData("contact", data)}
                  onNext={handleNext}
                  onBack={handleBack}
                />
              </TabsContent>

              <TabsContent value="review">
                <BusinessReviewForm
                  formData={formData}
                  onBack={handleBack}
                  onSubmit={handleSubmit}
                  isSubmitting={isSubmitting}
                />
              </TabsContent>
            </Tabs>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
