"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { OnboardingGuard } from "@/components/onboarding-guard"
import {
  BarChart3,
  Building2,
  Calendar,
  Clock,
  Edit,
  Info,
  MessageSquare,
  Phone,
  Settings,
  Star,
  Upload,
  Users,
} from "lucide-react"

export default function BusinessDashboardPage() {
  const [activeTab, setActiveTab] = useState("overview")

  // Mock business data
  const business = {
    name: "Denver Mobile Notary",
    description: "Professional mobile notary services available 24/7 throughout the Denver metro area.",
    category: "Notaries",
    additionalCategories: ["Legal Services"],
    rating: 4.8,
    reviewCount: 32,
    phone: "(*************",
    address: "1234 Main St, Denver, CO 80202",
    is24Hours: true,
    isVerified: true,
    joinDate: "January 15, 2023",
    profileViews: 1243,
    callClicks: 87,
    messageRequests: 45,
    upcomingAppointments: 3,
  }

  // Mock reviews
  const reviews = [
    {
      id: 1,
      name: "Sarah M.",
      date: "2 weeks ago",
      rating: 5,
      content: "Excellent service! They arrived on time and were very professional. Highly recommend!",
    },
    {
      id: 2,
      name: "John D.",
      date: "1 month ago",
      rating: 5,
      content: "Very responsive and came to my location at 11pm when I needed urgent document notarization.",
    },
    {
      id: 3,
      name: "Emily L.",
      date: "2 months ago",
      rating: 4,
      content: "Good service overall. Slightly higher price than others but worth it for the convenience.",
    },
  ]

  return (
    <OnboardingGuard requireOnboardingComplete={true}>
      <div className="container mx-auto max-w-6xl px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold">Business Dashboard</h1>
          <p className="text-gray-500">Manage your business listing and view performance metrics</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </Button>
          <Link href="/business/dashboard/edit">
            <Button className="gap-2">
              <Edit className="h-4 w-4" />
              Edit Listing
            </Button>
          </Link>
        </div>
      </div>

      <Alert className="mb-8 bg-blue-50 border-blue-200">
        <Info className="h-4 w-4 text-blue-600" />
        <AlertTitle className="text-blue-800">Welcome to Your Dashboard!</AlertTitle>
        <AlertDescription className="text-blue-700">
          Your business is pending review. The data shown below is for demonstration purposes and will be replaced with real data once your listing is active.
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Profile Views</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{business.profileViews}</div>
            <p className="text-xs text-gray-500">Last 30 days</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Call Clicks</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{business.callClicks}</div>
            <p className="text-xs text-gray-500">Last 30 days</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Message Requests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{business.messageRequests}</div>
            <p className="text-xs text-gray-500">Last 30 days</p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 md:w-auto">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
          <TabsTrigger value="appointments">Appointments</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Business Profile</CardTitle>
              <CardDescription>View and manage your business information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="md:w-1/3">
                  <div className="aspect-video relative rounded-md overflow-hidden mb-4">
                    <Image
                      src="/placeholder.svg?height=200&width=400"
                      alt={business.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <Button variant="outline" className="w-full gap-2">
                    <Upload className="h-4 w-4" />
                    Update Photos
                  </Button>
                </div>
                <div className="md:w-2/3">
                  <div className="flex items-center gap-2 mb-2">
                    <h2 className="text-xl font-bold">{business.name}</h2>
                    {business.isVerified && (
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                        Verified
                      </Badge>
                    )}
                    {business.is24Hours && (
                      <Badge variant="outline" className="bg-green-50 text-green-700">
                        24/7
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-1 mb-4">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${
                            i < Math.floor(business.rating) ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-600">
                      {business.rating} ({business.reviewCount} reviews)
                    </span>
                  </div>
                  <p className="text-gray-600 mb-4">{business.description}</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-5 w-5 text-gray-500" />
                      <div>
                        <div className="text-sm text-gray-500">Category</div>
                        <div className="font-medium">{business.category}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-5 w-5 text-gray-500" />
                      <div>
                        <div className="text-sm text-gray-500">Phone</div>
                        <div className="font-medium">{business.phone}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-5 w-5 text-gray-500" />
                      <div>
                        <div className="text-sm text-gray-500">Hours</div>
                        <div className="font-medium">{business.is24Hours ? "Open 24/7" : "Set your hours"}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-5 w-5 text-gray-500" />
                      <div>
                        <div className="text-sm text-gray-500">Member Since</div>
                        <div className="font-medium">{business.joinDate}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button variant="outline">View Public Profile</Button>
            </CardFooter>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Reviews</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {reviews.slice(0, 2).map((review) => (
                    <div key={review.id} className="border-b pb-4 last:border-0 last:pb-0">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium">{review.name}</div>
                          <div className="text-sm text-gray-500">{review.date}</div>
                        </div>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-3 w-3 ${
                                i < review.rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                      <p className="text-sm mt-2">{review.content}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="ghost" size="sm" className="ml-auto" onClick={() => setActiveTab("reviews")}>
                  View All Reviews
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Upcoming Appointments</CardTitle>
              </CardHeader>
              <CardContent>
                {business.upcomingAppointments > 0 ? (
                  <div className="space-y-4">
                    <div className="border-b pb-4">
                      <div className="font-medium">Document Notarization</div>
                      <div className="text-sm text-gray-500">Today, 3:00 PM</div>
                      <div className="text-sm mt-1">Client: Michael Johnson</div>
                      <div className="text-sm">Location: Client's Office</div>
                    </div>
                    <div className="border-b pb-4">
                      <div className="font-medium">Real Estate Closing</div>
                      <div className="text-sm text-gray-500">Tomorrow, 10:00 AM</div>
                      <div className="text-sm mt-1">Client: Sarah Williams</div>
                      <div className="text-sm">Location: 555 Cherry St, Denver</div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <p className="text-gray-500">No upcoming appointments</p>
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="ghost" size="sm" className="ml-auto" onClick={() => setActiveTab("appointments")}>
                  View All Appointments
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="reviews" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Customer Reviews</CardTitle>
              <CardDescription>Manage and respond to customer reviews</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {reviews.map((review) => (
                  <div key={review.id} className="border-b pb-6 last:border-0">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="font-medium">{review.name}</div>
                        <div className="text-sm text-gray-500">{review.date}</div>
                      </div>
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < review.rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    <p className="mt-2">{review.content}</p>
                    <div className="mt-4 flex gap-2">
                      <Button variant="outline" size="sm">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Respond
                      </Button>
                      <Button variant="ghost" size="sm">
                        Report
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <div className="flex justify-between w-full">
                <Button variant="outline">Request Reviews</Button>
                <Button variant="outline">Download Reviews</Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="appointments" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Appointment Management</CardTitle>
              <CardDescription>View and manage your upcoming appointments</CardDescription>
            </CardHeader>
            <CardContent>
              {business.upcomingAppointments > 0 ? (
                <div className="space-y-4">
                  <div className="border-b pb-4">
                    <div className="font-medium">Document Notarization</div>
                    <div className="text-sm text-gray-500">Today, 3:00 PM</div>
                    <div className="text-sm mt-1">Client: Michael Johnson</div>
                    <div className="text-sm">Location: Client's Office</div>
                    <div className="text-sm">Phone: (*************</div>
                    <div className="mt-3 flex gap-2">
                      <Button variant="outline" size="sm">
                        Reschedule
                      </Button>
                      <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                        Cancel
                      </Button>
                    </div>
                  </div>
                  <div className="border-b pb-4">
                    <div className="font-medium">Real Estate Closing</div>
                    <div className="text-sm text-gray-500">Tomorrow, 10:00 AM</div>
                    <div className="text-sm mt-1">Client: Sarah Williams</div>
                    <div className="text-sm">Location: 555 Cherry St, Denver</div>
                    <div className="text-sm">Phone: (*************</div>
                    <div className="mt-3 flex gap-2">
                      <Button variant="outline" size="sm">
                        Reschedule
                      </Button>
                      <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                        Cancel
                      </Button>
                    </div>
                  </div>
                  <div className="border-b pb-4">
                    <div className="font-medium">Power of Attorney</div>
                    <div className="text-sm text-gray-500">Friday, 2:00 PM</div>
                    <div className="text-sm mt-1">Client: Robert Thompson</div>
                    <div className="text-sm">Location: 123 Main St, Denver</div>
                    <div className="text-sm">Phone: (*************</div>
                    <div className="mt-3 flex gap-2">
                      <Button variant="outline" size="sm">
                        Reschedule
                      </Button>
                      <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                        Cancel
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Appointments</h3>
                  <p className="text-gray-500 mb-4">You don't have any upcoming appointments.</p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button className="w-full">Set Availability</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Performance Analytics</CardTitle>
              <CardDescription>View insights about your business performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center bg-gray-50 rounded-md mb-6">
                <BarChart3 className="h-16 w-16 text-gray-300" />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-gray-500">Profile Views</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{business.profileViews}</div>
                    <p className="text-xs text-green-600">↑ 12% from last month</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-gray-500">Call Clicks</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{business.callClicks}</div>
                    <p className="text-xs text-green-600">↑ 8% from last month</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-gray-500">Conversion Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">7.0%</div>
                    <p className="text-xs text-red-600">↓ 2% from last month</p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                Download Report
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </OnboardingGuard>
  )
}
