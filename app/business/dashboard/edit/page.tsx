'use client'

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { OnboardingGuard } from "@/components/onboarding-guard"
import { AlertCircle, CheckCircle2, Loader2 } from "lucide-react"
import { getBusinessForUser, updateBusiness } from "@/app/auth-actions"
import { BusinessImageUpload } from "@/components/business-image-upload"
import { PhoneInput } from "@/components/phone-input"

export default function EditBusinessPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { data: session, status } = useSession()
  const [formData, setFormData] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [error, setError] = useState("")

  useEffect(() => {
    if (status === "loading") return; // Wait until session status is resolved
    if (status === "unauthenticated") {
      router.push("/login?redirect=/business/dashboard/edit");
      return;
    }

    if (status === "authenticated" && session?.user?.id) {
      const businessId = searchParams.get('id')

      if (businessId) {
        // Load specific business by ID
        getBusinessForUser(session.user.id)
          .then(data => {
            if (data && data.id === businessId) {
              setFormData(data)
            } else {
              setError("Business not found or you don't have permission to edit it.")
            }
            setIsLoading(false)
          })
          .catch(() => {
            setError("Failed to load business data.")
            setIsLoading(false)
          })
      } else {
        // Load user's business (original behavior)
        getBusinessForUser(session.user.id)
          .then(data => {
            if (data) {
              setFormData(data)
            } else {
              setError("No business found for this user.")
            }
            setIsLoading(false)
          })
          .catch(() => {
            setError("Failed to load business data.")
            setIsLoading(false)
          })
      }
    } else if (status === "authenticated") {
      // Session is authenticated, but user id is not yet available.
      // This can happen in a race condition. We'll wait for the next render.
      setIsLoading(true);
    }
  }, [status, session, router])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => (prev ? { ...prev, [name]: value } : null))
  }

  const handleSubmit = async () => {
    if (!formData) return

    setIsSubmitting(true)
    setError("")

    try {
      const result = await updateBusiness(formData)
      if (result.success) {
        setIsSuccess(true)
        setTimeout(() => {
          // Redirect to the registered business profile page
          router.push(`/business/registered/${formData.id}`)
        }, 2000)
      } else {
        setError(result.error || "Failed to update business.")
      }
    } catch (err) {
      setError("An unexpected error occurred.")
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        <p className="ml-4 text-gray-500">Loading your business data...</p>
      </div>
    )
  }

  if (error && !formData) {
    return (
      <div className="container max-w-4xl mx-auto py-12 px-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!formData) {
    return (
      <div className="container max-w-4xl mx-auto py-12 px-4">
        <Card>
          <CardHeader>
            <CardTitle>No Business Found</CardTitle>
          </CardHeader>
          <CardContent>
            <p>We couldn't find a registered business for your account.</p>
            <Button onClick={() => router.push("/business/register")} className="mt-4">
              Register Your Business
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <OnboardingGuard requireOnboardingComplete={true}>
      <div className="container max-w-4xl mx-auto py-12 px-4">
      <Card>
        <CardHeader>
          <CardTitle>Edit Business Listing</CardTitle>
          <CardDescription>Update your business details below. Changes will be reviewed before going live.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          {isSuccess && (
            <Alert className="bg-green-50 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Update Successful!</AlertTitle>
              <AlertDescription className="text-green-700">
                Your business details have been submitted for review.
              </AlertDescription>
            </Alert>
          )}
          <div className="space-y-4">
            <div>
              <label htmlFor="businessName" className="block text-sm font-medium text-gray-700">Business Name</label>
              <input type="text" name="businessName" id="businessName" value={formData.businessName} onChange={handleInputChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
            </div>
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">Description</label>
              <textarea name="description" id="description" value={formData.description} onChange={handleInputChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
            </div>

            {/* Business Image Upload */}
            <BusinessImageUpload
              currentImageUrl={formData.imageUrl}
              onImageChange={(imageUrl) => setFormData(prev => ({ ...prev, imageUrl }))}
              label="Business Image"
              description="Upload or update your business photo, logo, or storefront image."
              required={false}
            />

            <PhoneInput
              id="phone"
              value={formData.phone || ""}
              onChange={(phone) => setFormData(prev => ({ ...prev, phone }))}
              label="Phone Number"
              required={true}
            />
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email</label>
              <input type="email" name="email" id="email" value={formData.email} onChange={handleInputChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
            </div>
            <div>
              <label htmlFor="website" className="block text-sm font-medium text-gray-700">Website</label>
              <input type="url" name="website" id="website" value={formData.website} onChange={handleInputChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
            </div>
            <div>
              <label htmlFor="address" className="block text-sm font-medium text-gray-700">Address</label>
              <input type="text" name="address" id="address" value={formData.address} onChange={handleInputChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
            </div>
            <div>
              <label htmlFor="city" className="block text-sm font-medium text-gray-700">City</label>
              <input type="text" name="city" id="city" value={formData.city} onChange={handleInputChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
            </div>
            <div>
              <label htmlFor="state" className="block text-sm font-medium text-gray-700">State</label>
              <input type="text" name="state" id="state" value={formData.state} onChange={handleInputChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
            </div>
            <div>
              <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700">Zip Code</label>
              <input type="text" name="zipCode" id="zipCode" value={formData.zipCode} onChange={handleInputChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
            </div>
            <div>
              <label htmlFor="categories" className="block text-sm font-medium text-gray-700">Categories</label>
              <input type="text" name="categories" id="categories" value={formData.categories} onChange={handleInputChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
            </div>
            <div>
              <label htmlFor="yearEstablished" className="block text-sm font-medium text-gray-700">Year Established</label>
              <input type="text" name="yearEstablished" id="yearEstablished" value={formData.yearEstablished} onChange={handleInputChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
            </div>
            <Button onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </CardContent>
      </Card>
      </div>
    </OnboardingGuard>
  )
}
