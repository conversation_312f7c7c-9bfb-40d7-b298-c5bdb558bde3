import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { MapPin, Search } from "lucide-react"

export default function LocationsPage() {
  // Sample list of popular locations
  const popularLocations = [
    { name: "Denver, CO", count: 42 },
    { name: "Aurora, CO", count: 28 },
    { name: "Lakewood, CO", count: 23 },
    { name: "Boulder, CO", count: 19 },
    { name: "Fort Collins, CO", count: 17 },
    { name: "Colorado Springs, CO", count: 31 },
    { name: "Arvada, CO", count: 15 },
    { name: "Westminster, CO", count: 14 },
    { name: "Thornton, CO", count: 13 },
    { name: "Centennial, CO", count: 12 },
    { name: "Pueblo, CO", count: 11 },
    { name: "Greeley, CO", count: 10 },
  ]

  // Group locations by first letter for alphabetical listing
  const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("")

  return (
    <main className="container mx-auto max-w-6xl px-4 py-12">
      <div className="mb-8 text-center">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">Emergency Services by Location</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Find emergency services in your area or browse our directory by location.
        </p>
      </div>

      {/* Search by ZIP code */}
      <Card className="mb-12">
        <CardContent className="p-6">
          <div className="text-center max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold mb-4">Find Services Near You</h2>
            <p className="text-gray-600 mb-6">Enter your ZIP code or city to find emergency services in your area.</p>
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Enter ZIP code or city"
                  className="pl-10 h-12 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                />
              </div>
              <Button className="h-12">Search</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Popular locations */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Popular Locations</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {popularLocations.map((location) => (
            <Link
              key={location.name}
              href={`/search?location=${encodeURIComponent(location.name)}`}
              className="flex items-center p-4 border rounded-md hover:bg-gray-50 transition-colors"
            >
              <MapPin className="h-5 w-5 text-blue-600 mr-2 flex-shrink-0" />
              <span className="font-medium">{location.name}</span>
              <span className="ml-auto text-sm text-gray-500">{location.count}</span>
            </Link>
          ))}
        </div>
      </div>

      {/* Alphabetical listing */}
      <div>
        <h2 className="text-2xl font-bold mb-6">All Locations</h2>
        <div className="flex flex-wrap gap-2 mb-6">
          {alphabet.map((letter) => (
            <a
              key={letter}
              href={`#letter-${letter}`}
              className="w-8 h-8 flex items-center justify-center border rounded-md hover:bg-blue-50 hover:border-blue-200 transition-colors"
            >
              {letter}
            </a>
          ))}
        </div>

        <div className="space-y-6">
          {alphabet.map((letter) => (
            <div key={letter} id={`letter-${letter}`} className="scroll-mt-20">
              <h3 className="text-xl font-bold mb-4 border-b pb-2">{letter}</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                {/* Sample locations for demonstration - in a real app, these would be filtered by starting letter */}
                {letter === "A" && (
                  <>
                    <Link
                      href="/search?location=Aurora, CO"
                      className="p-2 hover:bg-gray-50 rounded-md transition-colors"
                    >
                      Aurora, CO
                    </Link>
                    <Link
                      href="/search?location=Arvada, CO"
                      className="p-2 hover:bg-gray-50 rounded-md transition-colors"
                    >
                      Arvada, CO
                    </Link>
                  </>
                )}
                {letter === "B" && (
                  <Link
                    href="/search?location=Boulder, CO"
                    className="p-2 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    Boulder, CO
                  </Link>
                )}
                {letter === "C" && (
                  <>
                    <Link
                      href="/search?location=Colorado Springs, CO"
                      className="p-2 hover:bg-gray-50 rounded-md transition-colors"
                    >
                      Colorado Springs, CO
                    </Link>
                    <Link
                      href="/search?location=Centennial, CO"
                      className="p-2 hover:bg-gray-50 rounded-md transition-colors"
                    >
                      Centennial, CO
                    </Link>
                  </>
                )}
                {letter === "D" && (
                  <Link
                    href="/search?location=Denver, CO"
                    className="p-2 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    Denver, CO
                  </Link>
                )}
                {letter === "F" && (
                  <Link
                    href="/search?location=Fort Collins, CO"
                    className="p-2 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    Fort Collins, CO
                  </Link>
                )}
                {letter === "G" && (
                  <Link
                    href="/search?location=Greeley, CO"
                    className="p-2 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    Greeley, CO
                  </Link>
                )}
                {letter === "L" && (
                  <Link
                    href="/search?location=Lakewood, CO"
                    className="p-2 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    Lakewood, CO
                  </Link>
                )}
                {letter === "P" && (
                  <Link
                    href="/search?location=Pueblo, CO"
                    className="p-2 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    Pueblo, CO
                  </Link>
                )}
                {letter === "T" && (
                  <Link
                    href="/search?location=Thornton, CO"
                    className="p-2 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    Thornton, CO
                  </Link>
                )}
                {letter === "W" && (
                  <Link
                    href="/search?location=Westminster, CO"
                    className="p-2 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    Westminster, CO
                  </Link>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </main>
  )
}
