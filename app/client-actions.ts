'use client'

import { Logger } from '@logtail/next';

// Fallback function to get approximate location based on IP address
// Using a hardcoded fallback location for Las Vegas (since we know we have data there)
// In a real app, you would use a proper IP geolocation service
function getLocationByIP(): { success: boolean; location?: string; error?: string; } {
  const log = new Logger();
  // Las Vegas coordinates as a fallback
  const fallbackLat = 36.1699;
  const fallbackLng = -115.1398;

  console.log("Using fallback location (Las Vegas)");
  log.info("Using fallback location", { city: "Las Vegas", lat: fallbackLat, lng: fallbackLng });

  return {
    success: true,
    location: `${fallbackLat.toFixed(6)},${fallbackLng.toFixed(6)}`,
  };
}

// Client-side function to get the user's location
export async function getUserLocation(): Promise<{
  success: boolean;
  location?: string;
  error?: string;
  usedFallback?: boolean;
}> {
  const log = new Logger();
  return new Promise(async (resolve) => {
    // Check if geolocation is supported
    if (!navigator.geolocation) {
      resolve({
        success: false,
        error: "Geolocation is not supported by your browser"
      });
      return;
    }

    // Set a backup timeout in case the geolocation API hangs
    const backupTimeout = setTimeout(() => {
      console.warn("Geolocation request timed out at the application level");

      // Use fallback location instead of showing an error
      console.log("Using fallback location due to timeout");
      const ipResult = getLocationByIP();
      resolve({
        success: true,
        location: `${ipResult.location}&approximate=true`,
        usedFallback: true
      });
    }, 5000); // Reduced to 5 seconds for better user experience

    try {
      // Get current position with progressive fallback strategy
      const getPositionWithFallback = async () => {
        // First try with high accuracy
        navigator.geolocation.getCurrentPosition(
          // Success callback
          (position) => {
            clearTimeout(backupTimeout);
            const { latitude, longitude } = position.coords;
            const locationParam = `${latitude.toFixed(6)},${longitude.toFixed(6)}`;

            console.log("Successfully obtained location:", { latitude, longitude });

            resolve({
              success: true,
              location: locationParam
            });
          },
          // Error callback for high accuracy - try again with lower accuracy
          async (highAccuracyError) => {
            console.warn("High accuracy location failed, trying with lower accuracy", highAccuracyError);

            // If high accuracy fails, try with lower accuracy
            navigator.geolocation.getCurrentPosition(
              // Success callback for low accuracy
              (position) => {
                clearTimeout(backupTimeout);
                const { latitude, longitude } = position.coords;
                const locationParam = `${latitude.toFixed(6)},${longitude.toFixed(6)}`;

                console.log("Obtained location with lower accuracy:", { latitude, longitude });

                resolve({
                  success: true,
                  location: locationParam
                });
              },
              // Final error callback - try IP-based fallback
              async (error) => {
                clearTimeout(backupTimeout);
                let errorMessage = "Unable to retrieve your location.";

                // Log detailed error information
                console.error("Geolocation error:", error);

                // Try IP-based fallback
                console.log("Trying IP-based geolocation fallback...");
                const ipResult = getLocationByIP();

                if (ipResult.success && ipResult.location) {
                  console.log("IP-based fallback succeeded");
                  resolve({
                    success: true,
                    location: `${ipResult.location}&approximate=true`,
                    usedFallback: true
                  });
                  return;
                }

                switch (error.code) {
                  case error.PERMISSION_DENIED:
                    errorMessage = "Location access was denied. Please enable location services in your browser settings.";
                    break;
                  case error.POSITION_UNAVAILABLE:
                    errorMessage = "Location information is unavailable. Please try again later or enter your location manually.";
                    break;
                  case error.TIMEOUT:
                    errorMessage = "The request to get your location timed out. Please try again or enter your location manually.";
                    break;
                }

                // Handle CoreLocation specific errors
                if (error.message && error.message.includes("CoreLocation")) {
                  console.warn("CoreLocation specific error detected");
                  errorMessage = "Your device is having trouble determining your precise location. Please try again later or enter your location manually.";
                }

                resolve({
                  success: false,
                  error: errorMessage
                });
              },
              // Options for low accuracy attempt
              {
                enableHighAccuracy: false,
                timeout: 10000,
                maximumAge: 60000 // Allow cached positions up to 1 minute old
              }
            );
          },
          // Options for high accuracy attempt
          {
            enableHighAccuracy: true,
            timeout: 3000, // Even shorter timeout for high accuracy
            maximumAge: 60000 // Allow cached positions up to 1 minute old for faster response
          }
        );
      };

      await getPositionWithFallback();
    } catch (unexpectedError) {
      clearTimeout(backupTimeout);
      console.error("Unexpected error in geolocation:", unexpectedError);

      // Try IP-based fallback
      console.log("Trying IP-based geolocation fallback after unexpected error...");
      const ipResult = getLocationByIP();

      if (ipResult.success && ipResult.location) {
        console.log("IP-based fallback succeeded after unexpected error");
        resolve({
          success: true,
          location: `${ipResult.location}&approximate=true`,
          usedFallback: true
        });
        return;
      }

      resolve({
        success: false,
        error: "An unexpected error occurred while getting your location. Please try again or enter your location manually."
      });
    }
  });
}
