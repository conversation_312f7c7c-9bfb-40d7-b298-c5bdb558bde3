import Link from "next/link"
import Image from "next/image"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Clock, Search, ArrowRight } from "lucide-react"

export default function BlogPage() {
  // Sample blog posts
  const blogPosts = [
    {
      id: "preparing-home-emergencies",
      title: "10 Ways to Prepare Your Home for Emergencies",
      excerpt:
        "Learn how to prepare your home for unexpected emergencies with these essential tips that every homeowner should know.",
      category: "Home Safety",
      author: "<PERSON>",
      readTime: "8 min read",
      date: "June 15, 2023",
      image: "/placeholder.svg?height=200&width=400",
    },
    {
      id: "choosing-emergency-plumber",
      title: "How to Choose a Reliable Emergency Plumber",
      excerpt:
        "Finding a trustworthy emergency plumber can be challenging. Here's what to look for when you need urgent plumbing assistance.",
      category: "Plumbing",
      author: "<PERSON>",
      readTime: "6 min read",
      date: "May 28, 2023",
      image: "/placeholder.svg?height=200&width=400",
    },
    {
      id: "winter-power-outage",
      title: "Surviving a Winter Power Outage: Essential Steps",
      excerpt:
        "Power outages during winter can be dangerous. Learn how to stay safe and warm when the electricity goes out during cold weather.",
      category: "Electrical",
      author: "Robert Garcia",
      readTime: "7 min read",
      date: "April 10, 2023",
      image: "/placeholder.svg?height=200&width=400",
    },
    {
      id: "emergency-kit-essentials",
      title: "Building Your Home Emergency Kit: What You Need",
      excerpt:
        "A well-stocked emergency kit is crucial for any household. Discover the essential items you should include in your home emergency kit.",
      category: "Emergency Preparedness",
      author: "Aisha Patel",
      readTime: "5 min read",
      date: "March 22, 2023",
      image: "/placeholder.svg?height=200&width=400",
    },
    {
      id: "locksmith-scams",
      title: "How to Avoid Emergency Locksmith Scams",
      excerpt:
        "Emergency locksmith scams are on the rise. Learn how to identify legitimate services and protect yourself from fraudulent providers.",
      category: "Locksmith",
      author: "David Wilson",
      readTime: "6 min read",
      date: "February 15, 2023",
      image: "/placeholder.svg?height=200&width=400",
    },
    {
      id: "hvac-maintenance",
      title: "Preventative HVAC Maintenance to Avoid Emergencies",
      excerpt:
        "Regular HVAC maintenance can prevent costly emergency repairs. Follow these tips to keep your heating and cooling systems running smoothly.",
      category: "HVAC",
      author: "Jennifer Lee",
      readTime: "7 min read",
      date: "January 30, 2023",
      image: "/placeholder.svg?height=200&width=400",
    },
  ]

  // Featured post
  const featuredPost = {
    id: "emergency-service-costs",
    title: "Understanding Emergency Service Costs: What to Expect and How to Prepare",
    excerpt:
      "Emergency services often come with premium pricing. Learn about typical costs for different emergency services, how to budget for them, and ways to avoid price gouging during emergencies.",
    category: "Financial Planning",
    author: "Emily Rodriguez",
    readTime: "10 min read",
    date: "June 20, 2023",
    image: "/placeholder.svg?height=400&width=800",
  }

  // Blog categories
  const categories = [
    "All Categories",
    "Emergency Preparedness",
    "Home Safety",
    "Plumbing",
    "Electrical",
    "HVAC",
    "Locksmith",
    "Financial Planning",
    "Automotive",
  ]

  return (
    <main className="container mx-auto max-w-6xl px-4 py-12">
      <div className="mb-8 text-center">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">Emergency Services Blog</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Expert advice, tips, and insights to help you prepare for and handle emergencies.
        </p>
      </div>

      {/* Search */}
      <div className="mb-12 max-w-2xl mx-auto">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <Input placeholder="Search articles..." className="pl-10" />
        </div>
      </div>

      {/* Categories */}
      <div className="mb-12 overflow-x-auto">
        <div className="flex space-x-2 min-w-max">
          {categories.map((category) => (
            <Button
              key={category}
              variant={category === "All Categories" ? "default" : "outline"}
              className="whitespace-nowrap"
            >
              {category}
            </Button>
          ))}
        </div>
      </div>

      {/* Featured Post */}
      <div className="mb-12">
        <Card className="overflow-hidden">
          <div className="md:flex">
            <div className="md:w-1/2 relative h-64 md:h-auto">
              <Image
                src={featuredPost.image || "/placeholder.svg"}
                alt={featuredPost.title}
                fill
                className="object-cover"
              />
            </div>
            <div className="md:w-1/2 p-6 md:p-8 flex flex-col justify-between">
              <div>
                <Badge className="mb-2">{featuredPost.category}</Badge>
                <h2 className="text-2xl md:text-3xl font-bold mb-4">{featuredPost.title}</h2>
                <p className="text-gray-600 mb-4">{featuredPost.excerpt}</p>
                <div className="flex items-center text-sm text-gray-500 mb-6">
                  <span className="font-medium mr-2">{featuredPost.author}</span>
                  <span className="mx-2">•</span>
                  <Clock className="h-4 w-4 mr-1" />
                  <span className="mr-2">{featuredPost.readTime}</span>
                  <span className="mx-2">•</span>
                  <span>{featuredPost.date}</span>
                </div>
              </div>
              <Button asChild>
                <Link href={`/blog/${featuredPost.id}`}>Read Article</Link>
              </Button>
            </div>
          </div>
        </Card>
      </div>

      {/* All Posts */}
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Latest Articles</h2>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              Most Recent
            </Button>
            <Button variant="outline" size="sm">
              Most Popular
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {blogPosts.map((post) => (
            <Card key={post.id} className="overflow-hidden flex flex-col h-full">
              <div className="relative h-48">
                <Image src={post.image || "/placeholder.svg"} alt={post.title} fill className="object-cover" />
              </div>
              <CardContent className="p-6 flex-grow">
                <Badge className="mb-2">{post.category}</Badge>
                <h3 className="text-xl font-bold mb-2">{post.title}</h3>
                <p className="text-gray-600 mb-4">{post.excerpt}</p>
                <div className="flex items-center text-sm text-gray-500">
                  <span className="font-medium mr-2">{post.author}</span>
                  <span className="mx-2">•</span>
                  <Clock className="h-4 w-4 mr-1" />
                  <span>{post.readTime}</span>
                </div>
              </CardContent>
              <CardFooter className="px-6 pb-6 pt-0">
                <Link
                  href={`/blog/${post.id}`}
                  className="text-blue-600 hover:text-blue-800 font-medium flex items-center"
                >
                  Read more
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>

        <div className="mt-8 text-center">
          <Button variant="outline" size="lg">
            Load More Articles
          </Button>
        </div>
      </div>

      {/* Newsletter */}
      <div className="mt-16 bg-blue-50 rounded-lg p-8">
        <div className="max-w-2xl mx-auto text-center">
          <h2 className="text-2xl font-bold mb-4">Subscribe to Our Newsletter</h2>
          <p className="text-gray-600 mb-6">
            Get the latest emergency preparedness tips, guides, and news delivered to your inbox.
          </p>
          <div className="flex flex-col sm:flex-row gap-2">
            <Input placeholder="Your email address" className="sm:flex-1" />
            <Button>Subscribe</Button>
          </div>
          <p className="text-xs text-gray-500 mt-4">
            By subscribing, you agree to our Privacy Policy. You can unsubscribe at any time.
          </p>
        </div>
      </div>
    </main>
  )
}
