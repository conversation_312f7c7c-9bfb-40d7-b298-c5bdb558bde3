# Detailed Plan for Better Stack Logging Implementation

The goal is to integrate Better Stack logging into your Next.js application by adding a Better Stack log statement directly below every existing `console.log` statement, while keeping the original `console.log` for local development visibility. This will involve:

1.  **Installing the `@logtail/next` package.**
2.  **Configuring environment variables** for Better Stack.
3.  **Wrapping the Next.js config** with `withBetterStack`.
4.  **Replacing `console.log` statements** with appropriate Better Stack logging methods based on their context (client, server, API route, utility).

Here's the step-by-step plan:

### **Phase 1: Setup and Configuration**

**Step 1: Install `@logtail/next`**
   - Install the Better Stack Next.js NPM package.

**Step 2: Set Environment Variables**
   - Add `NEXT_PUBLIC_BETTER_STACK_SOURCE_TOKEN` and `NEXT_PUBLIC_BETTER_STACK_INGESTING_URL` to your `.env.local` file (or equivalent environment variable setup for production). These will be requested during the implementation phase.

**Step 3: Wrap Next.js Config**
   - Modify `next.config.mjs` to import `withBetterStack` and wrap the `nextConfig` object.

### **Phase 2: Replacing `console.log` Statements**

This phase will involve iterating through the identified files and replacing `console.log` statements. The approach will vary based on the file type:

*   **Server-side files (API Routes, Server Components, `lib/` utility files used on the server):**
    *   For API Route Handlers (e.g., `app/api/search/route.ts`), wrap the export with `withBetterStack` and use `req.log.info()` or `req.log.debug()`.
    *   For Server Components (e.g., `app/layout.tsx` if it contains `console.log` in a server context), import `Logger` and create an instance (`const log = new Logger();`), then use `log.info()` or `log.debug()`. Remember to call `await log.flush();` before returning.
    *   For `lib/` files (`lib/db-access.ts`, `lib/search-tracking-service.ts`) and `scripts/` files, these are typically server-side or build-time scripts. A `Logger` instance will be introduced to these files. Since these are not Next.js components or API routes, `Logger` will be instantiated directly.

*   **Client-side files (Client Components):**
    *   For client components (e.g., `components/search-filters.tsx`, `components/popular-services.tsx`, `components/search.tsx`, `app/client-actions.ts`), the `useLogger` hook will be used.

**General Approach for each `console.log`:**
For each `console.log(message, ...args)`:
1.  Determine the context (client, server, API route, utility).
2.  If it's a client component, ensure `'use client'` is at the top of the file.
3.  Import the appropriate logger (`Logger` or `useLogger`).
4.  Instantiate the logger if needed.
5.  Add a new line directly underneath the `console.log` with the equivalent Better Stack log statement. For example:
    ```javascript
    console.log("My log message", data);
    log.info("My log message", { data }); // Or log.debug, log.warn, log.error
    ```
    `log.info` will be used for general informational logs and `log.debug` for more verbose debugging logs, based on the context of the original `console.log`. If the `console.log` is an error, `log.error` will be used.

### **Phase 3: Verification and Cleanup**

**Step 1: Test Locally**
   - Ensure the application still functions correctly and logs appear in the console as before.

**Step 2: Verify Better Stack Logs**
   - Check your Better Stack Live Tail to confirm that logs are being ingested.

**Mermaid Diagram for the overall process:**

```mermaid
graph TD
    A[User Request: Implement Better Stack Logging] --> B{Information Gathering};
    B --> C{Check Next.js Version};
    B --> D{Find all console.log instances};
    B --> E{Read next.config.mjs};
    C --> F{Next.js Version Compatible?};
    F -- Yes --> G[Detailed Plan Creation];
    F -- No --> H[Inform User & Suggest Upgrade];
    G --> I[Phase 1: Setup & Configuration];
    I --> I1[Install @logtail/next];
    I --> I2[Set Environment Variables];
    I --> I3[Wrap Next.js Config];
    G --> J[Phase 2: Replacing console.log Statements];
    J --> J1{Identify Log Context};
    J1 -- API Route --> J1a[Wrap with withBetterStack];
    J1 -- Server Component --> J1b[Instantiate Logger & Flush];
    J1 -- Client Component --> J1c[Use useLogger Hook];
    J1 -- Utility/Script --> J1d[Instantiate Logger directly];
    J --> J2[Add Better Stack Log below each console.log];
    G --> K[Phase 3: Verification & Cleanup];
    K --> K1[Test Locally];
    K --> K2[Verify Logs in Better Stack];
    K --> L[Attempt Completion];