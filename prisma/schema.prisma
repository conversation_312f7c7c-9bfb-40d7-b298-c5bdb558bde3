// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Business {
  id             String  @id
  name           String
  categories     String?
  phone          String?
  address        String?
  city           String?
  term           String?
  url            String?
  rating         Float?
  reviewCount    Int?    @map("review_count")
  isClosed       Int?    @map("is_closed")
  latitude       Float?
  longitude      Float?
  zipCode        String? @map("zip_code")
  region         String?
  lastUpdated    String  @map("last_updated")
  price          String?
  imageUrl       String? @map("image_url")
  photos         String?
  hours          String?
  transactions   String?
  attributes     String?
  crossStreets   String? @map("cross_streets")
  neighborhood   String?
  displayAddress String? @map("display_address")

  @@map("businesses")

  @@index([term])
  @@index([city])
  @@index([region])
  @@index([zipCode])
  @@index([categories])
}

model GeocodeCache {
  key         String  @id
  zipCode     String? @map("zip_code")
  region      String?
  lastUpdated String  @map("last_updated")

  @@map("geocode_cache")
}

model CompletionTracking {
  id          Int     @id @default(autoincrement())
  completedAt String  @map("completed_at")
  isFullRun   Boolean @map("is_full_run") @default(true)

  @@map("completion_tracking")
}

model ScriptState {
  id          Int    @id @default(autoincrement())
  cityIndex   Int    @map("city_index")
  termIndex   Int    @map("term_index")
  offset      Int
  lastUpdated String @map("last_updated")

  @@map("script_state")
}
