/*
  Warnings:

  - You are about to drop the column `hasCompletedOnboarding` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `onboardingCompletedAt` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `onboardingStartedAt` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `onboardingStep` on the `users` table. All the data in the column will be lost.

*/
-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_users" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "email" TEXT NOT NULL,
    "name" TEXT,
    "passwordHash" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "role" TEXT NOT NULL DEFAULT 'USER',
    "isEmailVerified" BOOLEAN NOT NULL DEFAULT false,
    "verificationToken" TEXT,
    "resetToken" TEXT,
    "resetTokenExpiry" DATETIME
);
INSERT INTO "new_users" ("createdAt", "email", "id", "isEmailVerified", "name", "passwordHash", "resetToken", "resetTokenExpiry", "role", "updatedAt", "verificationToken") SELECT "createdAt", "email", "id", "isEmailVerified", "name", "passwordHash", "resetToken", "resetTokenExpiry", "role", "updatedAt", "verificationToken" FROM "users";
DROP TABLE "users";
ALTER TABLE "new_users" RENAME TO "users";
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
