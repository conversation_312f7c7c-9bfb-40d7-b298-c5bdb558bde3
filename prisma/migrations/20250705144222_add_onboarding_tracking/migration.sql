-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "email" TEXT NOT NULL,
    "name" TEXT,
    "passwordHash" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "role" TEXT NOT NULL DEFAULT 'USER',
    "isEmailVerified" BOOLEAN NOT NULL DEFAULT false,
    "verificationToken" TEXT,
    "resetToken" TEXT,
    "resetTokenExpiry" DATETIME,
    "hasCompletedOnboarding" BOOLEAN NOT NULL DEFAULT false,
    "onboardingStartedAt" DATETIME,
    "onboardingCompletedAt" DATETIME,
    "onboardingStep" TEXT
);

-- CreateTable
CREATE TABLE "saved_businesses" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "businessId" TEXT NOT NULL,
    "savedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "notes" TEXT,
    CONSTRAINT "saved_businesses_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "search_history" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT,
    "searchQuery" TEXT NOT NULL,
    "searchLocation" TEXT,
    "searchedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "search_history_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "search_term_stats" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "searchTerm" TEXT NOT NULL,
    "searchCount" INTEGER NOT NULL DEFAULT 1,
    "lastSearchedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "business_view_stats" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "businessId" TEXT NOT NULL,
    "businessName" TEXT NOT NULL,
    "viewCount" INTEGER NOT NULL DEFAULT 1,
    "lastViewedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "registered_businesses" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "businessName" TEXT NOT NULL,
    "description" TEXT,
    "phone" TEXT NOT NULL,
    "email" TEXT,
    "website" TEXT,
    "address" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "state" TEXT NOT NULL,
    "zipCode" TEXT NOT NULL,
    "categories" TEXT NOT NULL,
    "is24Hours" BOOLEAN NOT NULL DEFAULT false,
    "yearEstablished" TEXT,
    "hoursOfOperation" TEXT,
    "status" TEXT NOT NULL DEFAULT 'PENDING_REVIEW',
    "externalBusinessId" TEXT,
    "imageUrl" TEXT,
    "reviewedBy" TEXT,
    "reviewedAt" DATETIME,
    "adminNotes" TEXT,
    "rejectionReason" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "registered_businesses_reviewedBy_fkey" FOREIGN KEY ("reviewedBy") REFERENCES "users" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "saved_businesses_userId_businessId_key" ON "saved_businesses"("userId", "businessId");

-- CreateIndex
CREATE INDEX "search_history_userId_searchedAt_idx" ON "search_history"("userId", "searchedAt");

-- CreateIndex
CREATE INDEX "search_history_searchedAt_idx" ON "search_history"("searchedAt");

-- CreateIndex
CREATE UNIQUE INDEX "search_term_stats_searchTerm_key" ON "search_term_stats"("searchTerm");

-- CreateIndex
CREATE INDEX "search_term_stats_searchCount_lastSearchedAt_idx" ON "search_term_stats"("searchCount", "lastSearchedAt");

-- CreateIndex
CREATE INDEX "search_term_stats_lastSearchedAt_idx" ON "search_term_stats"("lastSearchedAt");

-- CreateIndex
CREATE UNIQUE INDEX "business_view_stats_businessId_key" ON "business_view_stats"("businessId");

-- CreateIndex
CREATE INDEX "business_view_stats_viewCount_lastViewedAt_idx" ON "business_view_stats"("viewCount", "lastViewedAt");

-- CreateIndex
CREATE INDEX "business_view_stats_lastViewedAt_idx" ON "business_view_stats"("lastViewedAt");
