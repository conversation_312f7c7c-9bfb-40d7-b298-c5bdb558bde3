// User accounts and billing database schema
// This database is persistent and not replaced

generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/user-prisma"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "sqlite"
  url      = env("USER_DATABASE_URL")
}

model User {
  id                String    @id @default(uuid())
  email             String    @unique
  name              String?
  passwordHash      String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  role              String    @default("USER") // USER, ADMIN, SUPER_ADMIN
  isEmailVerified   <PERSON>olean   @default(false)
  verificationToken String?
  resetToken        String?
  resetTokenExpiry  DateTime?

  // Relations within this database only
  savedBusinesses   SavedBusiness[]
  searchHistory     SearchHistory[]
  reviewedBusinesses RegisteredBusiness[] @relation("BusinessReviewer")

  @@map("users")
}

// Store business IDs as strings without foreign key constraints
model SavedBusiness {
  id              String   @id @default(uuid())
  userId          String
  businessId      String   // Reference to Business.id in the other database
  savedAt         DateTime @default(now())
  notes           String?

  // Only relate to tables in the same database
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, businessId])
  @@map("saved_businesses")
}

// Track individual user search history for "Recent Searches" feature
model SearchHistory {
  id              String   @id @default(uuid())
  userId          String?  // Optional - can track anonymous searches too
  searchQuery     String   // What the user searched for
  searchLocation  String?  // Where they searched (city, zip, etc.)
  searchedAt      DateTime @default(now())

  // Only relate to tables in the same database
  user            User?    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, searchedAt])
  @@index([searchedAt])
  @@map("search_history")
}

// Track global search term statistics for "Popular Emergency Services" feature
model SearchTermStats {
  id              String   @id @default(uuid())
  searchTerm      String   @unique // The search term (normalized)
  searchCount     Int      @default(1) // How many times this term has been searched
  lastSearchedAt  DateTime @default(now()) // When this term was last searched
  createdAt       DateTime @default(now())

  @@index([searchCount, lastSearchedAt])
  @@index([lastSearchedAt])
  @@map("search_term_stats")
}

// Track business view/click statistics for "Popular Emergency Services" feature
model BusinessViewStats {
  id              String   @id @default(uuid())
  businessId      String   @unique // Reference to Business.id in the main database
  businessName    String   // Cache the business name for easier querying
  viewCount       Int      @default(1) // How many times this business has been viewed
  lastViewedAt    DateTime @default(now()) // When this business was last viewed
  createdAt       DateTime @default(now())

  @@index([viewCount, lastViewedAt])
  @@index([lastViewedAt])
  @@map("business_view_stats")
}

// Allow users to register their own business
model RegisteredBusiness {
  id                 String   @id @default(uuid())
  userId             String   // Foreign key to User table
  businessName       String
  description        String?
  phone              String
  email              String?
  website            String?
  address            String
  city               String
  state              String
  zipCode            String
  categories         String   // Comma-separated list of categories
  is24Hours          Boolean  @default(false)
  yearEstablished    String?
  hoursOfOperation   String?  // JSON string of hours
  status             String   @default("PENDING_REVIEW") // PENDING_REVIEW, APPROVED, REJECTED, NEEDS_CHANGES
  externalBusinessId String?  // Link to main business database
  imageUrl           String?  // Path to uploaded business image

  // Review workflow fields
  reviewedBy         String?  // Admin user ID who reviewed
  reviewedAt         DateTime? // When the review was completed
  adminNotes         String?  // Admin feedback/notes
  rejectionReason    String?  // Specific reason for rejection

  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  // Relations
  reviewer           User?    @relation("BusinessReviewer", fields: [reviewedBy], references: [id])

  @@map("registered_businesses")
}


